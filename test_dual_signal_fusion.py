#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双信号融合分析算法测试脚本
测试从张力振动双信号融合分析.py提取的算法的核心功能
"""

import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 直接实现算法核心函数，避免GUI依赖
class DualSignalFusionTester:
    def __init__(self):
        pass

    def preprocess_data(self, Z):
        """数据预处理函数 - 去除突发性噪声"""
        processed_Z = Z.copy()
        n = len(processed_Z)
        for i in range(2, n - 1):
            delta_prev = processed_Z[i-1] - processed_Z[i-2]
            delta_current = processed_Z[i] - processed_Z[i-1]
            delta_next = processed_Z[i+1] - processed_Z[i]
            condition1 = abs(delta_current) > 2 * abs(delta_prev)
            condition2 = abs(delta_current) > 3 * abs(delta_next)
            if condition1 and condition2:
                processed_Z[i] = processed_Z[i-1]
        return processed_Z

    def compute_maad(self, Z, avg, window_size=3):
        """计算滑动窗口MAAD"""
        n = len(Z)
        maad = []
        for i in range(n - window_size + 1):
            window = Z[i:i+window_size]
            deviations = np.abs(window - avg)
            maad.append(np.mean(deviations))
        return maad

    def standardize_and_level(self, maad):
        """标准化与等级划分"""
        mu = np.mean(maad)
        sigma = np.std(maad)
        z_scores = (maad - mu) / sigma
        levels = []
        for z in z_scores:
            if z > 3:
                levels.append(3)
            elif z > 2:
                levels.append(2)
            elif z > 1:
                levels.append(1)
            else:
                levels.append(0)
        return levels

    def vibrate_score(self, Z_x, Z_y, Z_z):
        """振动得分算法"""
        # 1. 数据预处理
        processed_Zx = self.preprocess_data(Z_x)
        processed_Zy = self.preprocess_data(Z_y)
        processed_Zz = self.preprocess_data(Z_z)

        # 2. 计算基准值
        avg_x = np.mean(processed_Zx[50:min(500, len(processed_Zx))])
        avg_y = np.mean(processed_Zy[50:min(500, len(processed_Zy))])
        avg_z = np.mean(processed_Zz[50:min(500, len(processed_Zz))])

        # 3. 计算MAAD
        window_size = 3
        maad_x = self.compute_maad(processed_Zx, avg_x, window_size)
        maad_y = self.compute_maad(processed_Zy, avg_y, window_size)
        maad_z = self.compute_maad(processed_Zz, avg_z, window_size)

        # 4. 标准化与等级划分
        levels_x = self.standardize_and_level(maad_x)
        levels_y = self.standardize_and_level(maad_y)
        levels_z = self.standardize_and_level(maad_z)

        # 5. 组合评分J
        J = [x + y + z for x, y, z in zip(levels_x, levels_y, levels_z)]

        # 方向系数和概率计算
        sigma_D_x = np.std(processed_Zx)
        sigma_D_y = np.std(processed_Zy)
        sigma_D_z = np.std(processed_Zz)

        P_values = []
        risk_alerts = []

        for k in range(len(maad_x)):
            t = k + int(window_size/2) - 1  # 对应原始数据索引
            if t >= len(processed_Zx):
                break

            # 计算方向系数
            Z_i_x = processed_Zx[t]
            Z_i_y = processed_Zy[t]
            Z_i_z = processed_Zz[t]

            z_D_x = (avg_x - Z_i_x) / sigma_D_x if sigma_D_x != 0 else 0
            yudia_x = -1 if z_D_x < 0 else 1

            z_D_y = (avg_y - Z_i_y) / sigma_D_y if sigma_D_y != 0 else 0
            yudia_y = -1 if z_D_y < 0 else 1

            z_D_z = (avg_z - Z_i_z) / sigma_D_z if sigma_D_z != 0 else 0
            yudia_z = -1 if z_D_z < 0 else 1

            yudia_total = np.sign(yudia_x + yudia_y + yudia_z)

            # 计算概率P
            j_score = J[k]
            if j_score > 1.5:
                P = 1.0 * yudia_total
            elif j_score > 1.1:
                P = 0.8 * yudia_total
            elif j_score > 1:
                P = 0.5 * yudia_total
            else:
                P = 0

            P_values.append(P)

            # 风险预警触发
            if abs(P) >= 1:
                risk_alerts.append(t)

        return risk_alerts, P_values

    def detect_anomalies_tension(self, data, window=15, k1=3, k2=0.7):
        """张力异常检测"""
        s = pd.Series(data)
        ma = s.rolling(window=window, min_periods=window).mean()
        sigma = s.rolling(window=window, min_periods=window).std(ddof=0)
        abs_diff = (s - ma).abs()

        cond_l1 = abs_diff > k1 * sigma
        cond_l2 = abs_diff > k2 * ma

        flags = pd.Series(0, index=s.index, dtype=int)
        flags.loc[cond_l1] = 1
        flags.loc[cond_l2 & ~cond_l1] = 2

        return flags > 0

    def detect_fusion_anomalies(self, tension_anomalies, vibration_anomalies, delay_window=1.0, sampling_rate=10):
        """双信号融合检测"""
        window_size = int(delay_window * sampling_rate)
        n = len(tension_anomalies)

        alarm_flags = np.zeros(n, dtype=bool)

        for i in np.where(tension_anomalies)[0]:
            start = max(0, i - window_size)
            end = min(i + window_size, n-1)
            if np.any(vibration_anomalies[start:end+1]):
                alarm_flags[start:end+1] = True

        for i in np.where(vibration_anomalies)[0]:
            start = max(0, i - window_size)
            end = min(i + window_size, n-1)
            if np.any(tension_anomalies[start:end+1]):
                alarm_flags[start:end+1] = True

        return alarm_flags

def test_dual_signal_fusion():
    """测试双信号融合分析算法"""
    print("=== 双信号融合分析算法测试 ===\n")

    # 创建测试数据
    print("1. 创建测试数据...")
    n_points = 1000
    time = np.arange(n_points)

    # 生成基础信号
    np.random.seed(42)  # 固定随机种子以便复现
    base_tension = 100 + 10 * np.sin(time * 0.01) + np.random.normal(0, 2, n_points)
    base_vibration_x = 0.5 * np.sin(time * 0.02) + np.random.normal(0, 0.1, n_points)
    base_vibration_y = 0.3 * np.cos(time * 0.015) + np.random.normal(0, 0.08, n_points)
    base_vibration_z = 0.4 * np.sin(time * 0.025) + np.random.normal(0, 0.12, n_points)

    # 添加异常点
    anomaly_positions = [200, 300, 500, 700, 850]
    for pos in anomaly_positions:
        if pos < n_points:
            # 张力异常
            base_tension[pos:pos+5] += 50
            # 振动异常 (稍有延迟)
            if pos + 3 < n_points:
                base_vibration_x[pos+2:pos+7] += 2.0
                base_vibration_y[pos+2:pos+7] += 1.5
                base_vibration_z[pos+2:pos+7] += 1.8

    print(f"   生成 {n_points} 个数据点")
    print(f"   添加 {len(anomaly_positions)} 个异常点: {anomaly_positions}")

    # 创建检测器实例
    print("\n2. 测试双信号融合分析算法...")
    tester = DualSignalFusionTester()

    # 测试各个算法组件
    print("\n3. 测试算法组件:")

    # 3.1 数据预处理测试
    print("   3.1 数据预处理测试")
    processed_x = tester.preprocess_data(base_vibration_x)
    noise_removed = np.sum(np.abs(processed_x - base_vibration_x) > 0.001)
    print(f"       处理了 {noise_removed} 个噪声点")

    # 3.2 MAAD计算测试
    print("   3.2 MAAD计算测试")
    avg_x = np.mean(processed_x[50:500])
    maad_x = tester.compute_maad(processed_x, avg_x, 3)
    print(f"       计算了 {len(maad_x)} 个MAAD值，均值: {np.mean(maad_x):.4f}")

    # 3.3 等级划分测试
    print("   3.3 等级划分测试")
    levels_x = tester.standardize_and_level(maad_x)
    level_counts = {i: levels_x.count(i) for i in range(4)}
    print(f"       等级分布: {level_counts}")

    # 3.4 振动得分测试
    print("   3.4 振动得分测试")
    risk_alerts, P_values = tester.vibrate_score(base_vibration_x, base_vibration_y, base_vibration_z)
    print(f"       检测到 {len(risk_alerts)} 个振动异常点")
    print(f"       异常点位置: {risk_alerts[:10]}")  # 显示前10个

    # 3.5 张力异常检测测试
    print("   3.5 张力异常检测测试")
    tension_anomalies = tester.detect_anomalies_tension(base_tension)
    tension_count = np.sum(tension_anomalies)
    tension_positions = np.where(tension_anomalies)[0]
    print(f"       检测到 {tension_count} 个张力异常点")
    print(f"       异常点位置: {tension_positions[:10].tolist()}")  # 显示前10个

    # 3.6 双信号融合测试
    print("   3.6 双信号融合测试")
    vibration_anomalies = np.zeros(len(base_tension), dtype=bool)
    vibration_anomalies[risk_alerts] = True

    fusion_anomalies = tester.detect_fusion_anomalies(
        tension_anomalies.values, vibration_anomalies, delay_window=1.0, sampling_rate=10)
    fusion_count = np.sum(fusion_anomalies)
    fusion_positions = np.where(fusion_anomalies)[0]
    print(f"       融合后检测到 {fusion_count} 个异常点")
    print(f"       融合异常位置: {fusion_positions[:10].tolist()}")  # 显示前10个

    # 4. 准确性分析
    print("\n4. 准确性分析:")
    print("=" * 50)

    # 计算各方法的准确性
    methods_results = {
        '振动检测': (risk_alerts, len(risk_alerts)),
        '张力检测': (tension_positions.tolist(), tension_count),
        '融合检测': (fusion_positions.tolist(), fusion_count)
    }

    for method_name, (detected_pos, count) in methods_results.items():
        print(f"\n{method_name}:")
        print(f"  检测数量: {count}")

        # 计算与真实异常点的匹配度 (允许±10的误差)
        matches = 0
        matched_true = []
        matched_detected = []

        for true_pos in anomaly_positions:
            for det_pos in detected_pos:
                if abs(det_pos - true_pos) <= 10:
                    matches += 1
                    matched_true.append(true_pos)
                    matched_detected.append(det_pos)
                    break

        precision = matches / len(detected_pos) if detected_pos else 0
        recall = matches / len(anomaly_positions) if anomaly_positions else 0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

        print(f"  匹配数量: {matches}")
        print(f"  精确率: {precision:.2f} ({matches}/{len(detected_pos)})")
        print(f"  召回率: {recall:.2f} ({matches}/{len(anomaly_positions)})")
        print(f"  F1分数: {f1_score:.2f}")

        if matched_true:
            print(f"  匹配的真实异常: {matched_true}")
            print(f"  对应检测位置: {matched_detected}")

    print(f"\n=== 算法特点总结 ===")
    print("双信号融合分析算法包含以下核心组件:")
    print("1. 数据预处理 - 去除突发性噪声")
    print("2. MAAD计算 - 滑动窗口平均绝对偏差")
    print("3. 标准化等级划分 - Z分数转换为0-3等级")
    print("4. 三轴振动评分 - 综合X/Y/Z轴信息")
    print("5. 张力滑动窗口检测 - L1突发+L2趋势异常")
    print("6. 时间窗口融合 - 1秒延迟窗口内的信号关联")
    print("\n算法优势:")
    print("• 高精度: 结合张力与振动双信号特征")
    print("• 低误报: 通过时间相关性验证减少误报")
    print("• 实时性: 适合在线监测应用场景")
    print("• 鲁棒性: 多层次检测机制提高可靠性")

if __name__ == '__main__':
    # 设置matplotlib支持中文
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    test_dual_signal_fusion()
    print("\n测试完成!")
