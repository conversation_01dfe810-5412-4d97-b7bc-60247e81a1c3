#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中文界面和红色圆点标记的脚本
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def test_chinese_markers():
    """测试中文标签和红色圆点标记"""
    print("测试中文界面和红色圆点标记...")
    
    # 创建测试数据
    time = np.arange(1000)
    tension = 3 + 2 * np.sin(time * 0.01) + 0.5 * np.random.normal(0, 1, 1000)
    vibration = 0.1 * np.sin(time * 0.02) + 0.05 * np.random.normal(0, 1, 1000)
    
    # 添加一些异常点
    tension_anomalies = np.random.choice(1000, size=20, replace=False)
    vibration_anomalies = np.random.choice(1000, size=15, replace=False)
    
    tension[tension_anomalies] += np.random.normal(0, 3, len(tension_anomalies))
    vibration[vibration_anomalies] += np.random.normal(0, 0.2, len(vibration_anomalies))
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    # 绘制张力数据
    ax1.plot(time, tension, 'b-', alpha=0.7, label='张力')
    ax1.scatter(time[tension_anomalies], tension[tension_anomalies],
               color='red', s=35, marker='o', edgecolor='darkred', linewidth=1,
               label=f'张力异常 ({len(tension_anomalies)})', alpha=0.8, zorder=5)
    
    ax1.set_ylabel('张力 (N)', fontsize=12, fontweight='bold')
    ax1.set_title('张力与振动异常检测分析', fontsize=14, fontweight='bold', pad=20)
    ax1.legend(loc='upper right', fontsize=10)
    ax1.grid(True, alpha=0.3)
    
    # 绘制振动数据
    ax2.plot(time, vibration, 'g-', alpha=0.7, label='振动 WT1_X')
    ax2.scatter(time[vibration_anomalies], vibration[vibration_anomalies],
               color='red', s=35, marker='o', edgecolor='darkred', linewidth=1,
               alpha=0.8, zorder=5)
    
    ax2.set_xlabel('时间 (秒)', fontsize=12, fontweight='bold')
    ax2.set_ylabel('振动幅度 (Hz)', fontsize=12, fontweight='bold')
    ax2.legend(loc='upper right', fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    # 添加信息文本
    info_text = f'振动异常总数: {len(vibration_anomalies)}'
    ax2.text(0.02, 0.98, info_text,
            transform=ax2.transAxes, fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8),
            verticalalignment='top')
    
    plt.tight_layout()
    plt.savefig('中文界面测试.png', dpi=150, bbox_inches='tight')
    print("已保存测试图表为 '中文界面测试.png'")
    plt.close()

def test_ui_elements():
    """测试界面元素的中文化"""
    print("\n测试界面元素中文化...")
    
    ui_elements = {
        "窗口标题": "张力与振动异常检测",
        "控制面板": "控制面板",
        "选择传感器": "选择传感器:",
        "选择轴向": "选择轴向:",
        "检测方法": "检测方法:",
        "敏感度": "敏感度:",
        "数据采样": "数据采样:",
        "大数据自动调整": "大数据自动调整",
        "更新分析": "更新分析",
        "导出异常": "导出异常",
        "重置缩放": "重置缩放",
        "异常检测结果": "异常检测结果"
    }
    
    print("界面元素中文化检查:")
    for key, value in ui_elements.items():
        print(f"  ✅ {key}: {value}")

def test_detection_methods():
    """测试检测方法的中文化"""
    print("\n测试检测方法中文化...")
    
    methods = [
        "Z-Score (标准差)",
        "IQR (四分位距)",
        "张力-振动相关性",
        "综合检测"
    ]
    
    print("检测方法中文化:")
    for i, method in enumerate(methods, 1):
        print(f"  {i}. {method}")

def test_marker_specifications():
    """测试标记规格"""
    print("\n测试标记规格...")
    
    specs = {
        "标记类型": "圆形 (circle)",
        "标记颜色": "红色 (red)",
        "标记大小": "35px",
        "边框颜色": "深红色 (darkred)",
        "边框宽度": "1px",
        "透明度": "0.8",
        "层级": "5 (前景)"
    }
    
    print("异常标记规格:")
    for key, value in specs.items():
        print(f"  ✅ {key}: {value}")

def main():
    print("=" * 60)
    print("张力与振动异常检测系统 - 中文界面测试")
    print("=" * 60)
    
    # 运行所有测试
    test_chinese_markers()
    test_ui_elements()
    test_detection_methods()
    test_marker_specifications()
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print("✅ 异常标记已改为红色圆点 (35px)")
    print("✅ 控制面板已完全中文化")
    print("✅ 图表标题和标签已中文化")
    print("✅ 结果显示已中文化")
    print("✅ 导出功能已中文化")
    print("✅ 检测方法选项已中文化")
    print("=" * 60)

if __name__ == '__main__':
    main()
