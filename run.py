#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import subprocess
import os

def main():
    print("Vibration Data Analyzer")
    print("=" * 50)
    print("1. Anomaly Detector (NEW - detect abnormal points)")
    print("2. Combined Analyzer (tension & vibration together)")
    print("3. Basic Version (matplotlib, separate tabs)")
    print("4. Advanced Version (pyqtgraph, better performance)")
    print("5. Chinese Version (may have font issues)")
    print("6. Install Dependencies")
    print("7. Exit")
    print("=" * 50)
    
    while True:
        choice = input("Please select an option (1-7): ").strip()

        if choice == '1':
            print("Starting Anomaly Detector...")
            try:
                subprocess.run([sys.executable, "anomaly_detector.py"])
            except FileNotFoundError:
                print("Error: anomaly_detector.py not found!")
            except Exception as e:
                print(f"Error: {e}")
            break

        elif choice == '2':
            print("Starting Combined Analyzer...")
            try:
                subprocess.run([sys.executable, "combined_analyzer.py"])
            except FileNotFoundError:
                print("Error: combined_analyzer.py not found!")
            except Exception as e:
                print(f"Error: {e}")
            break

        elif choice == '3':
            print("Starting Basic Version...")
            try:
                subprocess.run([sys.executable, "vibration_analyzer.py"])
            except FileNotFoundError:
                print("Error: vibration_analyzer.py not found!")
            except Exception as e:
                print(f"Error: {e}")
            break

        elif choice == '4':
            print("Starting Advanced Version...")
            try:
                subprocess.run([sys.executable, "vibration_plotter.py"])
            except FileNotFoundError:
                print("Error: vibration_plotter.py not found!")
            except Exception as e:
                print(f"Error: {e}")
            break

        elif choice == '5':
            print("Starting Chinese Version...")
            try:
                subprocess.run([sys.executable, "simple_plotter.py"])
            except FileNotFoundError:
                print("Error: simple_plotter.py not found!")
            except Exception as e:
                print(f"Error: {e}")
            break

        elif choice == '6':
            print("Installing dependencies...")
            try:
                subprocess.run([sys.executable, "install_dependencies.py"])
            except FileNotFoundError:
                print("Error: install_dependencies.py not found!")
            except Exception as e:
                print(f"Error: {e}")
            input("Press Enter to continue...")

        elif choice == '7':
            print("Goodbye!")
            break

        else:
            print("Invalid choice. Please select 1-7.")

if __name__ == '__main__':
    main()
