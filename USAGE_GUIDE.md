# 振动数据分析器使用指南

## 快速开始

### 方法1：使用启动脚本（推荐）
```bash
python run.py
```
然后按照菜单选择相应的版本。

### 方法2：直接运行
```bash
# 组合分析器（推荐 - 张力和振动一起显示）
python combined_analyzer.py

# 基础版本（分标签页显示）
python vibration_analyzer.py

# 高级版本
python vibration_plotter.py
```

## 程序功能

### 1. 组合显示（推荐功能）
- **上下分布**：张力曲线在上方，振动曲线在下方
- **共享时间轴**：便于对比分析不同时间点的数据关系
- **交互辅助线**：鼠标移动时显示十字辅助线
- **数据点击**：点击图表获取精确数值

### 2. 张力曲线显示
- 显示张力随时间的变化
- 红色实线表示张力数据
- 显示平均值参考线
- 支持缩放和平移

### 3. 振动曲线显示
- 同时显示多个传感器的振动数据
- 支持选择不同的传感器（WT1-WT6）
- 支持选择不同的轴向（X、Y、Z）
- 不同颜色和线型区分不同传感器和轴向

### 4. 交互式功能
- **鼠标辅助线**：移动鼠标显示十字线，便于读取数值
- **点击获取数据**：点击图表在控制台输出精确数值
- **传感器选择**：勾选要显示的传感器
- **轴向选择**：选择X、Y、Z轴数据
- **实时更新**：修改选择后自动更新图表

## 界面说明

### 控制面板
- **Select Sensors**: 选择要显示的传感器（WT1-WT6）
- **Select Axes**: 选择要显示的轴向数据（X、Y、Z）
- **Update Charts**: 手动更新图表
- **Export Images**: 导出图表为PNG文件

### 图表标签页
- **Tension Curve**: 张力曲线图
- **Vibration Curves**: 振动数据图

将数据曲线放到一起，并添加鼠标交互功能（如辅助线）根据时间横轴，便于数据曲线的比较。

## 数据格式要求

CSV文件应包含以下列：
```
Date_Time,Tension,WT1_X,WT1_Y,WT1_Z,WT2_X,WT2_Y,WT2_Z,...
```

- `Date_Time`: 时间戳（格式：HH:MM:SS）
- `Tension`: 张力值
- `WT1_X`, `WT1_Y`, `WT1_Z`: 传感器1的三轴数据
- `WT2_X`, `WT2_Y`, `WT2_Z`: 传感器2的三轴数据
- ... 最多支持6个传感器

## 操作步骤

1. **启动程序**
   ```bash
   python vibration_analyzer.py
   ```

2. **选择传感器**
   - 在控制面板中勾选要显示的传感器
   - 默认选中前3个传感器（WT1、WT2、WT3）

3. **选择轴向**
   - 勾选要显示的轴向数据（X、Y、Z）
   - 默认全部选中

4. **查看图表**
   - 点击"Tension Curve"标签查看张力曲线
   - 点击"Vibration Curves"标签查看振动数据

5. **更新显示**
   - 修改传感器或轴向选择后，点击"Update Charts"

6. **导出图片**
   - 点击"Export Images"按钮
   - 图片将保存为tension_plot.png和vibration_plot.png

## 图表操作

### 基础版本（matplotlib）
- **缩放**: 使用鼠标滚轮
- **平移**: 按住鼠标左键拖动
- **重置**: 点击工具栏的Home按钮

### 高级版本（pyqtgraph）
- **缩放**: 鼠标滚轮或右键拖动
- **平移**: 左键拖动
- **重置**: 右键菜单选择"View All"

## 故障排除

### 1. 程序无法启动
```bash
# 检查Python版本
python --version

# 安装依赖
python install_dependencies.py
```

### 2. 图表不显示
- 检查CSV文件格式是否正确
- 确保选中了传感器和轴向
- 点击"Update Charts"按钮

### 3. 中文显示问题
- 使用英文版本：`python vibration_analyzer.py`
- 或安装中文字体支持

### 4. 性能问题
- 数据量大时使用高级版本：`python vibration_plotter.py`
- 减少同时显示的传感器数量

## 技术规格

- **支持的数据格式**: CSV
- **最大传感器数量**: 6个
- **每个传感器轴数**: 3个（X、Y、Z）
- **支持的Python版本**: 3.7+
- **依赖库**: PySide6, pandas, numpy, matplotlib, pyqtgraph

## 版本说明

- **vibration_analyzer.py**: 基础版本，使用matplotlib，兼容性好
- **vibration_plotter.py**: 高级版本，使用pyqtgraph，性能更好
- **simple_plotter.py**: 中文版本，可能有字体显示问题

选择建议：
- 新手用户：使用基础版本
- 大数据量：使用高级版本
- 需要中文界面：使用中文版本（需要字体支持）
