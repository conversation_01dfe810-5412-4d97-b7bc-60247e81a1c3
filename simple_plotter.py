#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 中文版
import sys
import pandas as pd
import numpy as np
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout,
                               QHBoxLayout, QWidget, QPushButton, QLabel,
                               QCheckBox, QGroupBox, QGridLayout, QTextEdit,
                               QSplitter, QSpinBox)
from PySide6.QtCore import Qt
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from datetime import datetime, timedelta
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import DBSCAN
import matplotlib # 通用的数据可视化库
matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

class MatplotlibCanvas(FigureCanvas):
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        super().__init__(self.fig)
        self.setParent(parent)

class SimplePlotter(QMainWindow):
    def __init__(self):
        super().__init__()
        self.data = None
        self.time_data = None
        self.init_ui()
        self.load_default_data()
        
    def init_ui(self):
        self.setWindowTitle('振动数据分析器 ')
        self.setGeometry(100, 100, 1200, 800)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 控制面板
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)
        
        # 创建组合图表（张力和振动在一起）
        self.combined_canvas = MatplotlibCanvas(self, width=12, height=10)
        main_layout.addWidget(self.combined_canvas)

        # 添加鼠标交互变量
        self.crosshair_v = None
        self.crosshair_h = None
        self.mouse_connected = False
        
    def create_control_panel(self):
        control_group = QGroupBox("控制面板")
        layout = QGridLayout(control_group)
        
        # 传感器选择
        sensor_label = QLabel("选择传感器:")
        layout.addWidget(sensor_label, 0, 0)
        
        self.sensor_checkboxes = {}
        sensors = ['WT1', 'WT2', 'WT3', 'WT4', 'WT5', 'WT6']
        
        for i, sensor in enumerate(sensors):
            checkbox = QCheckBox(sensor)
            checkbox.setChecked(i < 3)  # 默认选中前3个传感器
            checkbox.stateChanged.connect(self.update_plots)
            self.sensor_checkboxes[sensor] = checkbox
            layout.addWidget(checkbox, 0, i + 1)
        
        # 轴选择
        axis_label = QLabel("选择轴:")
        layout.addWidget(axis_label, 1, 0)
        
        self.axis_checkboxes = {}
        axes = ['X', 'Y', 'Z']
        for i, axis in enumerate(axes):
            checkbox = QCheckBox(axis)
            checkbox.setChecked(True)
            checkbox.stateChanged.connect(self.update_plots)
            self.axis_checkboxes[axis] = checkbox
            layout.addWidget(checkbox, 1, i + 1)
        
        # 更新按钮
        update_button = QPushButton("更新图表")
        update_button.clicked.connect(self.update_plots)
        layout.addWidget(update_button, 2, 0)
        
        return control_group
    
    def load_default_data(self):
        """加载默认的CSV文件"""
        try:
            self.data = pd.read_csv("7-24-20.csv")

            # 处理时间数据 - 创建相对时间（秒）
            self.time_data = np.arange(len(self.data))

            self.update_combined_plot()
        except Exception as e:
            print(f"无法加载默认文件: {str(e)}")

    def update_plots(self):
        """更新图表"""
        if self.data is None:
            return

        self.update_combined_plot()

    def setup_mouse_interaction(self):
        """设置鼠标交互功能"""
        if not self.mouse_connected:
            self.combined_canvas.mpl_connect('motion_notify_event', self.on_mouse_move)
            self.combined_canvas.mpl_connect('button_press_event', self.on_mouse_click)
            self.mouse_connected = True

    def on_mouse_move(self, event):
        """鼠标移动事件"""
        if event.inaxes is None:
            return

        # 更新辅助线位置
        if self.crosshair_v is not None:
            self.crosshair_v.set_xdata([event.xdata, event.xdata])
        if self.crosshair_h is not None:
            self.crosshair_h.set_ydata([event.ydata, event.ydata])

        self.combined_canvas.draw_idle()

    def on_mouse_click(self, event):
        """鼠标点击事件"""
        if event.inaxes is None:
            return

        # 在控制台输出点击位置的数据信息
        x_pos = int(event.xdata) if event.xdata is not None else 0
        if 0 <= x_pos < len(self.data):
            print(f"时间点: {x_pos}秒")
            if 'Tension' in self.data.columns:
                print(f"张力: {self.data.iloc[x_pos]['Tension']:.2f} N")

            # 显示选中传感器的振动数据
            selected_sensors = [sensor for sensor, checkbox in self.sensor_checkboxes.items()
                              if checkbox.isChecked()]
            selected_axes = [axis for axis, checkbox in self.axis_checkboxes.items()
                            if checkbox.isChecked()]

            for sensor in selected_sensors:
                for axis in selected_axes:
                    column_name = f"{sensor}_{axis}"
                    if column_name in self.data.columns:
                        value = self.data.iloc[x_pos][column_name]
                        print(f"{sensor}_{axis}: {value:.4f} g")
            print("-" * 30)
    
    def update_combined_plot(self):
        """更新组合图表（张力和振动在一起）"""
        self.combined_canvas.fig.clear()

        # 创建子图：上方显示张力，下方显示振动
        ax1 = self.combined_canvas.fig.add_subplot(211)  # 张力图
        ax2 = self.combined_canvas.fig.add_subplot(212, sharex=ax1)  # 振动图，共享x轴

        # 绘制张力曲线
        if 'Tension' in self.data.columns:
            tension_data = self.data['Tension'].values
            ax1.plot(self.time_data, tension_data, 'r-', linewidth=2, label='张力')
            ax1.set_ylabel('张力 (N)', fontsize=12)
            ax1.set_title('张力和振动数据对比分析', fontsize=14, fontweight='bold')
            ax1.grid(True, alpha=0.3)
            ax1.legend(loc='upper right')

        # 绘制振动曲线
        selected_sensors = [sensor for sensor, checkbox in self.sensor_checkboxes.items()
                          if checkbox.isChecked()]
        selected_axes = [axis for axis, checkbox in self.axis_checkboxes.items()
                        if checkbox.isChecked()]

        colors = ['red', 'green', 'blue', 'orange', 'purple', 'brown']
        line_styles = ['-', '--', ':']

        plot_count = 0
        for i, sensor in enumerate(selected_sensors):
            for j, axis in enumerate(selected_axes):
                column_name = f"{sensor}_{axis}"
                if column_name in self.data.columns:
                    vibration_data = self.data[column_name].values
                    color = colors[i % len(colors)]
                    style = line_styles[j % len(line_styles)]

                    ax2.plot(self.time_data, vibration_data,
                           color=color, linestyle=style, linewidth=1.5,
                           label=f"{sensor}_{axis}", alpha=0.8)
                    plot_count += 1

        if plot_count > 0:
            ax2.set_xlabel('时间 (秒)', fontsize=12)
            ax2.set_ylabel('振动幅度 (g)', fontsize=12)
            ax2.grid(True, alpha=0.3)
            ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

        # 添加辅助线
        if self.crosshair_v is None:
            # 垂直辅助线（时间轴）
            self.crosshair_v = ax1.axvline(color='gray', linestyle='--', alpha=0.7, linewidth=1)
            ax2.axvline(color='gray', linestyle='--', alpha=0.7, linewidth=1)

        if self.crosshair_h is None:
            # 水平辅助线
            self.crosshair_h = ax1.axhline(color='gray', linestyle='--', alpha=0.7, linewidth=1)

        # 调整布局
        self.combined_canvas.fig.tight_layout()
        self.combined_canvas.draw()

        # 设置鼠标交互
        self.setup_mouse_interaction()

def main():
    app = QApplication(sys.argv)
    
    window = SimplePlotter()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
