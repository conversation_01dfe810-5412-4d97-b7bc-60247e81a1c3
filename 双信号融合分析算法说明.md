# 双信号融合分析算法说明

## 概述

双信号融合分析算法是从`张力振动双信号融合分析.py`中提取的核心异常检测算法，现已集成到`anomaly_detector.py`中。该算法通过结合张力和振动双信号特征，实现高精度、低误报的异常检测。

## 算法架构

### 1. 数据预处理 (`preprocess_data`)

**功能**: 去除突发性噪声，平滑数据信号

**原理**: 
- 检测每个数据点与前后点的变化率
- 识别异常突变点（当前变化率远大于前后变化率）
- 用前一个点的值替换异常点

**判断条件**:
```python
condition1 = abs(delta_current) > 2 * abs(delta_prev)
condition2 = abs(delta_current) > 3 * abs(delta_next)
```

**应用**: 对三轴振动数据(X, Y, Z)进行预处理

### 2. MAAD计算 (`compute_maad`)

**功能**: 计算滑动窗口平均绝对偏差(Mean Absolute Average Deviation)

**原理**:
- 使用固定窗口大小(默认3)在信号上滑动
- 计算窗口内每个点相对于基准值的绝对偏差
- 取窗口内偏差的平均值作为MAAD值

**公式**:
```
MAAD[i] = mean(|window[i:i+window_size] - baseline|)
```

**意义**: 量化信号相对于稳定基准的偏离程度

### 3. 标准化与等级划分 (`standardize_and_level`)

**功能**: 将MAAD值转换为标准化等级

**步骤**:
1. 计算MAAD序列的均值μ和标准差σ
2. 计算Z分数: `z = (maad - μ) / σ`
3. 根据Z分数划分等级:
   - z > 3: 等级3 (严重异常)
   - z > 2: 等级2 (中度异常)  
   - z > 1: 等级1 (轻度异常)
   - 其他: 等级0 (正常)

### 4. 振动得分算法 (`vibrate_score`)

**功能**: 三轴振动综合异常检测的核心算法

**流程**:

#### 4.1 数据预处理
对X、Y、Z三轴振动数据分别进行预处理

#### 4.2 基准值计算
使用50-500点的均值作为稳定基准:
```python
avg_x = mean(processed_Zx[50:500])
avg_y = mean(processed_Zy[50:500])  
avg_z = mean(processed_Zz[50:500])
```

#### 4.3 MAAD计算
分别计算三轴的MAAD值

#### 4.4 等级划分
将三轴MAAD值分别转换为0-3等级

#### 4.5 组合评分
```python
J[i] = level_x[i] + level_y[i] + level_z[i]
```

#### 4.6 方向系数计算
```python
z_D_x = (avg_x - Z_i_x) / sigma_D_x
yudia_x = -1 if z_D_x < 0 else 1
yudia_total = sign(yudia_x + yudia_y + yudia_z)
```

#### 4.7 概率计算
根据组合评分J确定异常概率:
- J > 1.5: P = 1.0 × yudia_total (高风险)
- J > 1.1: P = 0.8 × yudia_total (中风险)
- J > 1.0: P = 0.5 × yudia_total (低风险)
- 其他: P = 0 (正常)

#### 4.8 风险预警
当 |P| ≥ 1 时触发风险预警

### 5. 张力异常检测 (`detect_anomalies_tension_sliding_window`)

**功能**: 基于滑动窗口的张力异常检测

**方法**:
- **L1检测**: 突发性异常 `|data - moving_avg| > k1 × moving_std`
- **L2检测**: 趋势性异常 `|data - moving_avg| > k2 × moving_avg`

**参数**:
- window=15: 滑动窗口大小
- k1=3: 突发性异常阈值系数
- k2=0.7: 趋势性异常阈值系数

### 6. 双信号融合检测 (`detect_anomalous_events_fusion`)

**功能**: 核心融合算法，检测张力和振动异常的时间相关性

**原理**:
1. 设置延迟窗口(默认1秒，10个采样点)
2. 以张力异常为触发点，在延迟窗口内寻找振动异常
3. 以振动异常为触发点，在延迟窗口内寻找张力异常
4. 只有在窗口内同时存在两种异常时才确认为真实异常

**优势**: 通过时间相关性验证大幅降低误报率

## 算法特点

### 优势

1. **高精度**: 结合张力与振动双信号特征，提高检测准确性
2. **低误报**: 通过时间窗口内的信号关联验证，减少单信号误报
3. **实时性**: 算法复杂度适中，适合在线监测应用
4. **鲁棒性**: 多层次检测机制，对噪声和干扰具有较强抗性
5. **可解释性**: 每个检测步骤都有明确的物理意义

### 适用场景

- 钻井作业异常监测
- 机械设备状态监控  
- 工业过程异常检测
- 需要高可靠性的安全监测系统

### 参数调优

- **敏感度调节**: 通过调整k1、k2系数控制检测敏感度
- **窗口大小**: 根据信号特性调整滑动窗口和延迟窗口大小
- **基准范围**: 根据数据稳定性调整基准值计算范围(50-500点)

## 使用方法

在`anomaly_detector.py`中选择"双信号融合分析"检测方法即可使用该算法。

### 前提条件

- 数据中需包含'Tension'列(张力数据)
- 数据中需包含完整的三轴振动数据(如WT1_X, WT1_Y, WT1_Z)

### 输出结果

- 异常点位置和数量
- 异常类型分类(张力/振动)
- 检测置信度信息
- 算法执行统计信息

## 技术实现

算法已完整集成到`AnomalyDetector`类中，包含以下核心方法:

- `preprocess_data()`: 数据预处理
- `compute_maad()`: MAAD计算  
- `standardize_and_level()`: 标准化等级划分
- `vibrate_score()`: 振动得分算法
- `detect_anomalies_tension_sliding_window()`: 张力异常检测
- `detect_anomalous_events_fusion()`: 双信号融合检测
- `detect_dual_signal_fusion()`: 主入口方法

通过这些方法的有机结合，实现了完整的双信号融合异常检测功能。
