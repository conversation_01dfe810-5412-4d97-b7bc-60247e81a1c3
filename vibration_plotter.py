#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 英文版 原生pyqtgraph
import sys
import pandas as pd
import numpy as np
from datetime import datetime
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout,
                               QHBoxLayout, QWidget, QPushButton, QLabel,
                               QComboBox, QCheckBox, QGroupBox, QGridLayout,
                               QFileDialog, QMessageBox)
from PySide6.QtCore import Qt
import pyqtgraph as pg
from pyqtgraph import PlotWidget, plot
import pyqtgraph.exporters

class VibrationPlotter(QMainWindow):
    def __init__(self):
        super().__init__()
        self.data = None
        self.time_data = None
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle('Vibration Data Analyzer')
        self.setGeometry(100, 100, 1200, 800)

        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create main layout
        main_layout = QVBoxLayout(central_widget)

        # Create control panel
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)

        # Create combined plot widget
        plot_widget = QWidget()
        plot_layout = QVBoxLayout(plot_widget)
        main_layout.addWidget(plot_widget)

        # Create tension plot (top)
        self.tension_plot = PlotWidget(title="Tension vs Time")
        self.tension_plot.setLabel('left', 'Tension', units='N')
        self.tension_plot.showGrid(x=True, y=True)
        self.tension_plot.setMaximumHeight(300)
        plot_layout.addWidget(self.tension_plot)

        # Create vibration plot (bottom)
        self.vibration_plot = PlotWidget(title="Vibration Data vs Time")
        self.vibration_plot.setLabel('left', 'Vibration Amplitude', units='Hz')
        self.vibration_plot.setLabel('bottom', 'Time (seconds)')
        self.vibration_plot.showGrid(x=True, y=True)
        plot_layout.addWidget(self.vibration_plot)

        # Add crosshair lines
        self.crosshair_v1 = pg.InfiniteLine(angle=90, movable=False, pen=pg.mkPen('gray', width=1, style=pg.QtCore.Qt.DashLine))
        self.crosshair_v2 = pg.InfiniteLine(angle=90, movable=False, pen=pg.mkPen('gray', width=1, style=pg.QtCore.Qt.DashLine))
        self.crosshair_h1 = pg.InfiniteLine(angle=0, movable=False, pen=pg.mkPen('gray', width=1, style=pg.QtCore.Qt.DashLine))
        self.crosshair_h2 = pg.InfiniteLine(angle=0, movable=False, pen=pg.mkPen('gray', width=1, style=pg.QtCore.Qt.DashLine))

        self.tension_plot.addItem(self.crosshair_v1, ignoreBounds=True)
        self.tension_plot.addItem(self.crosshair_h1, ignoreBounds=True)
        self.vibration_plot.addItem(self.crosshair_v2, ignoreBounds=True)
        self.vibration_plot.addItem(self.crosshair_h2, ignoreBounds=True)

        # Connect mouse events
        self.tension_plot.scene().sigMouseMoved.connect(self.mouse_moved)
        self.vibration_plot.scene().sigMouseMoved.connect(self.mouse_moved)
        self.tension_plot.scene().sigMouseClicked.connect(self.mouse_clicked)
        self.vibration_plot.scene().sigMouseClicked.connect(self.mouse_clicked)
        
        # 加载默认数据
        self.load_default_data()
        
    def create_control_panel(self):
        control_group = QGroupBox("Control Panel")
        layout = QGridLayout(control_group)

        # File selection
        self.load_button = QPushButton("Load CSV File")
        self.load_button.clicked.connect(self.load_csv_file)
        layout.addWidget(self.load_button, 0, 0)

        self.file_label = QLabel("Current file: 7-24-20.csv")
        layout.addWidget(self.file_label, 0, 1, 1, 2)

        # Sensor selection
        sensor_label = QLabel("Select Sensors:")
        layout.addWidget(sensor_label, 1, 0)

        self.sensor_checkboxes = {}
        sensors = ['WT1', 'WT2', 'WT3', 'WT4', 'WT5', 'WT6']
        colors = ['red', 'green', 'blue', 'yellow', 'magenta', 'cyan']

        for i, (sensor, color) in enumerate(zip(sensors, colors)):
            checkbox = QCheckBox(sensor)
            checkbox.setChecked(i < 3)  # Default: select first 3 sensors
            checkbox.stateChanged.connect(self.update_plots)
            self.sensor_checkboxes[sensor] = {'checkbox': checkbox, 'color': color}
            layout.addWidget(checkbox, 1, i + 1)

        # Axis selection
        axis_label = QLabel("Select Axes:")
        layout.addWidget(axis_label, 2, 0)

        self.axis_checkboxes = {}
        axes = ['X', 'Y', 'Z']
        for i, axis in enumerate(axes):
            checkbox = QCheckBox(axis)
            checkbox.setChecked(True)  # Default: select all
            checkbox.stateChanged.connect(self.update_plots)
            self.axis_checkboxes[axis] = checkbox
            layout.addWidget(checkbox, 2, i + 1)

        # Update button
        update_button = QPushButton("Update Charts")
        update_button.clicked.connect(self.update_plots)
        layout.addWidget(update_button, 3, 0)

        # Export button
        export_button = QPushButton("Export Images")
        export_button.clicked.connect(self.export_plots)
        layout.addWidget(export_button, 3, 1)
        
        return control_group
    
    def load_default_data(self):
        """Load default CSV file"""
        try:
            self.load_data("7-24-20.csv")
        except Exception as e:
            QMessageBox.warning(self, "Warning", f"Cannot load default file: {str(e)}")

    def load_csv_file(self):
        """Load CSV file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select CSV File", "", "CSV files (*.csv)")

        if file_path:
            try:
                self.load_data(file_path)
                self.file_label.setText(f"Current file: {file_path.split('/')[-1]}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load file: {str(e)}")

    def load_data(self, file_path):
        """Load data"""
        self.data = pd.read_csv(file_path)

        # Process time data - create relative time (seconds)
        self.time_data = np.arange(len(self.data))

        self.update_plots()

    def mouse_moved(self, pos):
        """Handle mouse movement for crosshair"""
        if self.data is None:
            return

        # Get the plot widget that sent the signal
        sender = self.sender()
        if hasattr(sender, 'parent') and hasattr(sender.parent(), 'vb'):
            vb = sender.parent().vb
            mouse_point = vb.mapSceneToView(pos)

            # Update crosshair positions
            x_pos = mouse_point.x()
            y_pos = mouse_point.y()

            self.crosshair_v1.setPos(x_pos)
            self.crosshair_v2.setPos(x_pos)
            self.crosshair_h1.setPos(y_pos)
            self.crosshair_h2.setPos(y_pos)

    def mouse_clicked(self, event):
        """Handle mouse click for data inspection"""
        if self.data is None:
            return

        # Get click position
        sender = self.sender()
        if hasattr(sender, 'parent') and hasattr(sender.parent(), 'vb'):
            vb = sender.parent().vb
            mouse_point = vb.mapSceneToView(event.scenePos())
            x_pos = int(mouse_point.x())

            if 0 <= x_pos < len(self.data):
                print(f"Time point: {x_pos} seconds")
                if 'Tension' in self.data.columns:
                    print(f"Tension: {self.data.iloc[x_pos]['Tension']:.2f} N")

                # Show selected sensor vibration data
                selected_sensors = [sensor for sensor, info in self.sensor_checkboxes.items()
                                  if info['checkbox'].isChecked()]
                selected_axes = [axis for axis, checkbox in self.axis_checkboxes.items()
                                if checkbox.isChecked()]

                for sensor in selected_sensors:
                    for axis in selected_axes:
                        column_name = f"{sensor}_{axis}"
                        if column_name in self.data.columns:
                            value = self.data.iloc[x_pos][column_name]
                            print(f"{sensor}_{axis}: {value:.4f} g")
                print("-" * 30)

    def update_plots(self):
        """Update charts"""
        if self.data is None:
            return

        self.update_tension_plot()
        self.update_vibration_plot()

    def update_tension_plot(self):
        """Update tension chart"""
        self.tension_plot.clear()

        if 'Tension' in self.data.columns:
            tension_data = self.data['Tension'].values
            self.tension_plot.plot(self.time_data, tension_data,
                                 pen=pg.mkPen(color='red', width=2),
                                 name='Tension')
    
    def update_vibration_plot(self):
        """Update vibration chart"""
        self.vibration_plot.clear()

        # Get selected sensors and axes
        selected_sensors = [sensor for sensor, info in self.sensor_checkboxes.items()
                          if info['checkbox'].isChecked()]
        selected_axes = [axis for axis, checkbox in self.axis_checkboxes.items()
                        if checkbox.isChecked()]

        # Plot selected sensor data
        for sensor in selected_sensors:
            color = self.sensor_checkboxes[sensor]['color']
            for axis in selected_axes:
                column_name = f"{sensor}_{axis}"
                if column_name in self.data.columns:
                    vibration_data = self.data[column_name].values
                    pen_style = pg.mkPen(color=color, width=1.5)
                    if axis == 'Y':
                        pen_style = pg.mkPen(color=color, width=1.5, style=Qt.DashLine)
                    elif axis == 'Z':
                        pen_style = pg.mkPen(color=color, width=1.5, style=Qt.DotLine)

                    self.vibration_plot.plot(self.time_data, vibration_data,
                                           pen=pen_style,
                                           name=f"{sensor}_{axis}")

    def export_plots(self):
        """Export images"""
        try:
            # Export tension plot
            tension_exporter = pg.exporters.ImageExporter(self.tension_plot.plotItem)
            tension_exporter.export('tension_plot.png')

            # Export vibration plot
            vibration_exporter = pg.exporters.ImageExporter(self.vibration_plot.plotItem)
            vibration_exporter.export('vibration_plot.png')

            QMessageBox.information(self, "Success", "Images exported as tension_plot.png and vibration_plot.png")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Export failed: {str(e)}")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    window = VibrationPlotter()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
