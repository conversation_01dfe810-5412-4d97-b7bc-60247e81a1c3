from matplotlib import font_manager, rcParams
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd

# 数据预处理函数
def preprocess_data(Z):
    processed_Z = Z.copy()
    n = len(processed_Z)
    for i in range(2, n - 1):
        delta_prev = processed_Z[i-1] - processed_Z[i-2]
        delta_current = processed_Z[i] - processed_Z[i-1]
        delta_next = processed_Z[i+1] - processed_Z[i]
        condition1 = abs(delta_current) > 2 * abs(delta_prev)
        condition2 = abs(delta_current) > 3 * abs(delta_next)
        if condition1 and condition2:
            processed_Z[i] = processed_Z[i-1]
    return processed_Z

# 计算滑动窗口MAAD
def compute_maad(Z, avg, window_size=3):
    n = len(Z)
    maad = []
    for i in range(n - window_size + 1):
        window = Z[i:i+window_size]
        deviations = np.abs(window - avg)
        maad.append(np.mean(deviations))
    return maad

# 标准化与等级划分
def standardize_and_level(maad):
    mu = np.mean(maad)
    sigma = np.std(maad)
    z_scores = (maad - mu) / sigma
    levels = []
    for z in z_scores:
        if z > 3:
            levels.append(3)
        elif z > 2:
            levels.append(2)
        elif z > 1:
            levels.append(1)
        else:
            levels.append(0)
    return levels

# 振动得分
def vibrate_score(Z_x, Z_y, Z_z):
    # 1. 数据预处理
    processed_Zx = preprocess_data(Z_x)
    processed_Zy = preprocess_data(Z_y)
    processed_Zz = preprocess_data(Z_z)
    
    # 2. 计算基准值
    avg_x = np.mean(processed_Zx[50:500])
    avg_y = np.mean(processed_Zy[50:500])
    avg_z = np.mean(processed_Zz[50:500])
    
    # 3. 计算MAAD
    window_size = 3
    maad_x = compute_maad(processed_Zx, avg_x, window_size)
    maad_y = compute_maad(processed_Zy, avg_y, window_size)
    maad_z = compute_maad(processed_Zz, avg_z, window_size)
    
    # 4. 标准化与等级划分
    levels_x = standardize_and_level(maad_x)
    levels_y = standardize_and_level(maad_y)
    levels_z = standardize_and_level(maad_z)
    
    # 5. 组合评分J
    J = [x + y + z for x, y, z in zip(levels_x, levels_y, levels_z)]
    
    # 方向系数和概率计算
    sigma_D_x = np.std(processed_Zx)
    sigma_D_y = np.std(processed_Zy)
    sigma_D_z = np.std(processed_Zz)
    
    P_values = []
    risk_alerts = []
    
    for k in range(len(maad_x)):
        t = k + int(window_size/2) - 1  # 对应原始数据索引
        if t >= len(processed_Zx):
            break
        
        # 计算方向系数
        Z_i_x = processed_Zx[t]
        Z_i_y = processed_Zy[t]
        Z_i_z = processed_Zz[t]
        
        z_D_x = (avg_x - Z_i_x) / sigma_D_x
        yudia_x = -1 if z_D_x < 0 else 1
        
        z_D_y = (avg_y - Z_i_y) / sigma_D_y
        yudia_y = -1 if z_D_y < 0 else 1
        
        z_D_z = (avg_z - Z_i_z) / sigma_D_z
        yudia_z = -1 if z_D_z < 0 else 1
        
        yudia_total = np.sign(yudia_x + yudia_y + yudia_z)
        
        # 计算概率P
        j_score = J[k]
        if j_score >1.5:
            P = 1.0 * yudia_total
        elif j_score > 1.1:
            P = 0.8 * yudia_total
        elif j_score > 1:
            P = 0.5 * yudia_total
        else:
            P = 0
        
        P_values.append(P)
        
        # 风险预警触发
        if abs(P) >= 1:
            risk_alerts.append(t)
    
    return risk_alerts, P_values

def detect_anomalies(data, window=15, k1=3, k2=0.7):
    s = pd.Series(data)
    ma = s.rolling(window=window, min_periods=window).mean()       # 移动均值
    sigma = s.rolling(window=window, min_periods=window).std(ddof=0)  # 标准差（分母N）
    abs_diff = (s - ma).abs()
    
    # 条件判断
    cond_l1 = abs_diff > k1 * sigma  # 突发性异常条件
    cond_l2 = abs_diff > k2 * ma     # 趋势性异常条件
    
    flags = pd.Series(0, index=s.index, dtype=int)  # 初始化标记为0
    flags.loc[cond_l1] = 1                          # L1优先标记
    flags.loc[cond_l2 & ~cond_l1] = 2               # L2次优先标记
    
    return flags

def _get_continuous_events(flags):
    events = []
    start = None
    for i, val in enumerate(flags):
        if val and start is None:
            start = i
        elif not val and start is not None:
            events.append((start, i-1))
            start = None
    if start is not None:
        events.append((start, len(flags)-1))
    
    return events

def speed_warning(speed, speed_diff):
    n = len(speed)
    alarm_flags = np.zeros(n, dtype=bool)
    for i in range(n):
        # 检查前五个点是否有报警点
        has_previous_alarm = False
        for j in range(1, 10):
            if i - j >= 0 and alarm_flags[i - j]:
                has_previous_alarm = True
                break
        
        if not has_previous_alarm:
            # 确保speed_diff[i]不是NaN值
            if pd.notna(speed_diff[i]) and (speed_diff[i] < 0) and (speed[i] == 0 or (i > 0 and speed[i-1] > 0 and speed[i] < 0)):
                alarm_flags[i] = True
    return _get_continuous_events(alarm_flags)

def calculate_accuracy(speed_alerts_events, other_alerts_events, window=50):
    # 将报警事件转换为单个点
    speed_alerts = []
    for event in speed_alerts_events:
        start, end = event
        speed_alerts.append((start + end) // 2)  # 取事件中间点作为代表
    
    other_alerts = []
    for event in other_alerts_events:
        start, end = event
        other_alerts.append((start + end) // 2)  # 取事件中间点作为代表
    
    speed_alerts_set = set()
    speed_alerts_positions = []
    for alert in speed_alerts:
        start = max(0, alert - window)
        end = min(len(speed), alert + window + 1)
        speed_alerts_set.update(range(start, end))
        speed_alerts_positions.append(alert)
    
    correct_count = 0
    correct_positions = []
    false_positions = []
    
    for alert in other_alerts:
        if alert in speed_alerts_set:
            correct_count += 1
            correct_positions.append(alert)
        else:
            false_positions.append(alert)
    
    return correct_count, correct_positions, false_positions

def calculate_missed_alarms(speed_alerts_events, other_alerts_events, window=50):
    # 将报警事件转换为单个点
    speed_alerts = []
    for event in speed_alerts_events:
        start, end = event
        speed_alerts.append((start + end) // 2)  # 取事件中间点作为代表
    
    other_alerts = []
    for event in other_alerts_events:
        start, end = event
        other_alerts.append((start + end) // 2)  # 取事件中间点作为代表
    
    missed_count = 0
    other_alerts_set = set(other_alerts)
    
    for alert in speed_alerts:
        start = max(0, alert - window)
        end = min(len(speed), alert + window + 1)
        has_other_alert = False
        for point in range(start, end + 1):
            if point in other_alerts_set:
                has_other_alert = True
                break
        if not has_other_alert:
            missed_count += 1
    
    return missed_count

def detect_anomalous_events(tension_anomalies, vibration_anomalies, delay_window, sampling_rate):
    window_size = int(delay_window * sampling_rate)
    n = len(tension_anomalies)
    
    alarm_flags = np.zeros(n, dtype=bool)
    
    for i in np.where(tension_anomalies)[0]:
        start = max(0, i - window_size)
        end = min(i + window_size, n-1)
        if np.any(vibration_anomalies[start:end+1]):
            alarm_flags[start:end+1] = True
    
    for i in np.where(vibration_anomalies)[0]:
        start = max(0, i - window_size)
        end = min(i + window_size, n-1)
        if np.any(tension_anomalies[start:end+1]):
            alarm_flags[start:end+1] = True
    
    return _get_continuous_events(alarm_flags)

# 设置字体以支持中文
font_path = '/System/Library/Fonts/Hiragino Sans GB.ttc'  # 请根据系统修改字体路径
font_prop = font_manager.FontProperties(fname=font_path, size=20)
rcParams['font.family'] = font_prop.get_name()
rcParams['axes.unicode_minus'] = False

if __name__ == '__main__':
    # 读取 CSV 文件
    # df = pd.read_csv("./华200-3-14归一化数据.csv")
    # df = pd.read_csv("./8-20处理好的数据_WT1Z最大值归一化.csv")
    df = pd.read_csv("/Users/<USER>/Desktop/数据整理/测井数据及数据处理工具/单信号分析/西41-117-138X(3400-3725点整合数据).csv")
    tension = df['Tension']
    Z_x = df['WT1_X'].values
    Z_y = df['WT1_Y'].values
    Z_z = df['WT1_Z'].values
    speed = df['Speed']
    print(f"speed: {speed}")
    speed_diff = speed.diff()
    print(f"speed_diff: {speed_diff}")

    x = df.index

    # 参数设置
    delay_window = 1.0  # 1秒延迟窗口
    sampling_rate = 10   # 每秒10个采样点

    # 计算张力分析报警点
    tension_flags = detect_anomalies(tension)
    tension_alerts = tension_flags[tension_flags > 0].index.tolist()

    # 计算振动分析报警点
    risk_alerts, P_values = vibrate_score(Z_x, Z_y, Z_z)

    # 计算速度报警点
    speed_alerts = speed_warning(speed, speed_diff)

    # 融合报警点
    tension_anomalies = np.zeros(len(tension), dtype=bool)
    tension_anomalies[tension_alerts] = True

    vibration_anomalies = np.zeros(len(tension), dtype=bool)
    vibration_anomalies[risk_alerts] = True

    fused_alerts = detect_anomalous_events(tension_anomalies, vibration_anomalies, delay_window, sampling_rate)

    # 计算融合方法的正确报警点和误报点
    fused_correct_count, fused_correct_positions, fused_false_positions = calculate_accuracy(speed_alerts, fused_alerts)
    fused_total = len(fused_alerts)
    fused_accuracy = fused_correct_count / fused_total if fused_total > 0 else 0
    fused_false_rate = (fused_total - fused_correct_count) / fused_total if fused_total > 0 else 0

    # 计算融合方法的漏报点
    fused_missed_count = calculate_missed_alarms(speed_alerts, fused_alerts)
    fused_missed_rate = fused_missed_count / len(speed_alerts) if len(speed_alerts) > 0 else 0

    # 打印速度报警点的位置和数量
    print(f"速度报警点数量: {len(speed_alerts)}")
    print("速度报警点位置:", speed_alerts)

    # 打印融合方法的正确报警点和误报点
    print(f"融合方法正确报警点数量: {fused_correct_count}")
    print(f"融合方法正确报警点位置: {fused_correct_positions}")
    print(f"融合方法误报点数量: {len(fused_false_positions)}")
    print(f"融合方法误报点位置: {fused_false_positions}")
    print(f"融合方法漏报点数量: {fused_missed_count}")
    print(f"融合方法漏报率: {fused_missed_rate:.2f}")

    # 打印正确率和误报率
    print(f"融合方法正确率: {fused_accuracy:.2f}, 误报率: {fused_false_rate:.2f}")

    ## 绘图
    plt.rcParams['figure.figsize'] = [12.8, 7.0]  # 设置图片大小为1280*700

    x_ticks = np.arange(0, len(tension), 500)  # 每500个点设置一个刻度
    fig,(ax1, ax2) = plt.subplots(2,1,sharex=True)

    # 绘制主要曲线
    ax1.plot(tension, label='Tension', linewidth=3)
    ax2.plot(Z_z, label='Vibration', linewidth=3)
    # ax3.plot(speed, label='Velocity', linewidth=3)

    ax1.set_xticks(x_ticks)
    ax1.tick_params(axis='both', labelsize=20)
    ax2.set_xticks(x_ticks)
    ax2.tick_params(axis='both', labelsize=20)
    ax2.set_xlabel('Time_Series', fontsize=25, ha='center', va='top', x=0.5)
    # ax3.set_xel('Time_Series', fontsize=25, ha='center', va='top', x=0.5)
# ticks(x_ticks)
#     ax3.tick_params(axis='both', labelsize=20)
#     ax3.set_xlab
    ax1.grid(True)
    ax2.grid(True)
    # ax3.grid(True)
    
    # 创建异常点的散点图，但只在第一次添加标签
    # 标注速度报警点
    # for i, (start, end) in enumerate(speed_alerts):
    #     mid = (start + end) // 2
    #     if i == 0:  # 只在第一次添加标签
    #         ax3.scatter(mid, speed[mid], color='red', s=50, zorder=5, label='Outlier')
    #     else:
    #         ax3.scatter(mid, speed[mid], color='red', s=50, zorder=5)

    # # 标注张力原始报警点
    # for i in tension_alerts:
    #     if 0 <= i < len(tension):
    #         ax1.scatter(i, tension[i], color='g', s=50, zorder=5, marker='x')

    # # 标注振动原始报警点
    # for i in risk_alerts:
    #     if 0 <= i < len(Z_z):
            # ax2.scatter(i, Z_z[i], color='g', s=50, zorder=5, marker='x')

    # 标注融合后的报警点（张力最小点）
    for i, event in enumerate(fused_alerts):
        start, end = event
        print(f"start: {start}, end: {end}")
        if start >= len(tension) or end >= len(tension):
            continue
        # 找到张力在当前事件范围内的最小值点
        min_tension_idx = np.argmin(tension[start:end+1]) + start
        
        # 只在第一次添加标签
        if i == 0:
            ax1.scatter(min_tension_idx, tension[min_tension_idx], color='r', s=50, zorder=5, label='Outlier')
            ax2.scatter(min_tension_idx, Z_z[min_tension_idx], color='r', s=50, zorder=5, label='Outlier')
        else:
            ax1.scatter(min_tension_idx, tension[min_tension_idx], color='r', s=50, zorder=5)
            ax2.scatter(min_tension_idx, Z_z[min_tension_idx], color='r', s=50, zorder=5)
    
    # 在所有绘图完成后，统一添加图例并放在右上角
    ax1.legend(prop=font_prop, loc='upper left')
    ax2.legend(prop=font_prop, loc='upper left')
    # ax3.legend(prop=font_prop, loc='upper left')

    plt.grid(True)
    plt.tight_layout(pad=0.5)
    plt.savefig(f'算法融合(138x_3400_3725).png', dpi=300, bbox_inches='tight')
    plt.show()

## 绘图 - 截取1660-1860范围的数据
    plt.rcParams['figure.figsize'] = [12.8, 7.0]  # 设置图片大小为1280*700

    # 定义截取范围
    start_idx = 1660
    end_idx = 1860
    
    # 截取数据
    tension_slice = tension[start_idx:end_idx]
    Z_z_slice = Z_z[start_idx:end_idx]
    speed_slice = speed[start_idx:end_idx]
    
    # 确保切片数据类型一致性
    is_tension_pandas = hasattr(tension_slice, 'iloc')
    is_speed_pandas = hasattr(speed_slice, 'iloc')
    
    # 创建从start_idx开始的x轴值
    x_values = np.arange(start_idx, end_idx)
    x_ticks = np.arange(start_idx, end_idx, 20)  # 每50个点设置一个刻度
    fig,(ax1, ax2) = plt.subplots(2,1,sharex=True)

    # 绘制主要曲线
    ax1.plot(x_values, tension_slice, label='Tension', linewidth=3)
    ax2.plot(x_values, Z_z_slice, label='Vibration', linewidth=3)
    # ax3.plot(x_values, speed_slice, label='Velocity', linewidth=3)

    ax1.set_xticks(x_ticks)
    ax1.tick_params(axis='both', labelsize=20)
    ax2.set_xticks(x_ticks)
    ax2.tick_params(axis='both', labelsize=20)
    ax2.set_xlabel('Time_Series', fontsize=25, ha='center', va='top', x=0.5)
    # ax3.set_xticks(x_ticks)
    # ax3.tick_params(axis='both', labelsize=20)
    # ax3.set_xlabel('Time_Series', fontsize=25, ha='center', va='top', x=0.5)

    ax1.grid(True)
    ax2.grid(True)
    # ax3.grid(True)
    
    # 创建异常点的散点图，但只在第一次添加标签
    # 标注速度报警点
    # speed_outlier_added = False
    # for i, (start, end) in enumerate(speed_alerts):
    #     mid = (start + end) // 2
    #     # 只显示在截取范围内的异常点
    #     if start_idx <= mid < end_idx:
    #         # 调整索引到截取数据的相对位置
    #         relative_mid = mid - start_idx
    #         # 确保相对索引在切片范围内
    #         if 0 <= relative_mid < len(speed_slice):
    #             if not speed_outlier_added:  # 只在第一次添加标签
    #                 speed_value = speed_slice.iloc[relative_mid] if is_speed_pandas else speed_slice[relative_mid]
    #                 ax3.scatter(start_idx + relative_mid, speed_value, color='red', s=50, zorder=5, label='Outlier')
    #                 speed_outlier_added = True
    #             else:
    #                 speed_value = speed_slice.iloc[relative_mid] if is_speed_pandas else speed_slice[relative_mid]
    #                 ax3.scatter(start_idx + relative_mid, speed_value, color='red', s=50, zorder=5)

    # 标注融合后的报警点（张力最小点）
    tension_outlier_added = False
    vibration_outlier_added = False
    
    for i, event in enumerate(fused_alerts):
        start, end = event
        if start >= len(tension) or end >= len(tension):
            continue
            
        # 找到张力在当前事件范围内的最小值点
        min_tension_idx = np.argmin(tension[start:end+1]) + start
        
        # 只显示在截取范围内的异常点
        if start_idx <= min_tension_idx < end_idx:
            # 调整索引到截取数据的相对位置
            relative_idx = min_tension_idx - start_idx
            
            # 确保相对索引在切片范围内
            if 0 <= relative_idx < len(tension_slice):
                # 张力图上的异常点
                if not tension_outlier_added:
                    tension_value = tension_slice.iloc[relative_idx] if is_tension_pandas else tension_slice[relative_idx]
                    ax1.scatter(start_idx + relative_idx, tension_value, color='r', s=50, zorder=5, label='Outlier')
                    tension_outlier_added = True
                else:
                    tension_value = tension_slice.iloc[relative_idx] if is_tension_pandas else tension_slice[relative_idx]
                    ax1.scatter(start_idx + relative_idx, tension_value, color='r', s=50, zorder=5)
                
                # 振动图上的异常点
                if not vibration_outlier_added:
                    # Z_z_slice是numpy数组，直接使用索引
                    ax2.scatter(start_idx + relative_idx, Z_z_slice[relative_idx], color='r', s=50, zorder=5, label='Outlier')
                    vibration_outlier_added = True
                else:
                    ax2.scatter(start_idx + relative_idx, Z_z_slice[relative_idx], color='r', s=50, zorder=5)
    
    # 在所有绘图完成后，统一添加图例并放在右上角
    ax1.legend(prop=font_prop, loc='upper left')
    ax2.legend(prop=font_prop, loc='upper left')
    # ax3.legend(prop=font_prop, loc='upper left')

    plt.grid(True)
    plt.tight_layout(pad=0.5)
    plt.savefig(f'算法融合(西41_117_137x_1500-3000)放大.png', dpi=300, bbox_inches='tight')
    plt.show()