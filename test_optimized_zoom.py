#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的缩放功能
"""

import numpy as np
import matplotlib.pyplot as plt

def test_zoom_improvements():
    """测试缩放功能的改进"""
    print("测试优化后的缩放功能...")
    
    # 创建测试数据
    time = np.arange(2000)
    tension = 3 + 2 * np.sin(time * 0.01) + 0.1 * np.random.normal(0, 1, 2000)
    vibration = 0.1 * np.sin(time * 0.02) + 0.05 * np.random.normal(0, 1, 2000)
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True)
    
    # 绘制数据
    ax1.plot(time, tension, 'b-', alpha=0.7, label='Tension')
    ax2.plot(time, vibration, 'g-', alpha=0.7, label='Vibration')
    
    # 模拟优化后的缩放事件处理
    def optimized_zoom(ax, zoom_in=True, mouse_x=None, mouse_y=None):
        """优化的缩放函数"""
        xlim = ax.get_xlim()
        ylim = ax.get_ylim()
        
        # 改进的缩放因子
        if zoom_in:
            zoom_factor_x = 0.8  # 更平滑的缩放
            zoom_factor_y = 0.9
        else:
            zoom_factor_x = 1.25
            zoom_factor_y = 1.1
        
        # 鼠标位置或中心点
        if mouse_x is not None and mouse_y is not None:
            x_center = mouse_x
            y_center = mouse_y
        else:
            x_center = (xlim[0] + xlim[1]) / 2
            y_center = (ylim[0] + ylim[1]) / 2
        
        # 计算新范围
        x_range = (xlim[1] - xlim[0]) * zoom_factor_x
        y_range = (ylim[1] - ylim[0]) * zoom_factor_y
        
        new_xlim = [x_center - x_range/2, x_center + x_range/2]
        new_ylim = [y_center - y_range/2, y_center + y_range/2]
        
        # 边界检查
        data_x_min, data_x_max = 0, len(time) - 1
        if new_xlim[1] - new_xlim[0] > data_x_max - data_x_min:
            new_xlim = [data_x_min, data_x_max]
        
        if new_xlim[1] - new_xlim[0] < 10:
            center = (new_xlim[0] + new_xlim[1]) / 2
            new_xlim = [center - 5, center + 5]
        
        return new_xlim, new_ylim
    
    # 测试不同的缩放场景
    print("\n缩放测试场景:")
    
    # 场景1：从全视图开始缩放
    original_xlim = ax1.get_xlim()
    print(f"1. 原始视图范围: {original_xlim[0]:.0f} - {original_xlim[1]:.0f}")
    
    # 模拟放大操作
    new_xlim, new_ylim = optimized_zoom(ax1, zoom_in=True, mouse_x=1000, mouse_y=3)
    print(f"   放大后范围: {new_xlim[0]:.0f} - {new_xlim[1]:.0f} (缩放比例: {(original_xlim[1]-original_xlim[0])/(new_xlim[1]-new_xlim[0]):.1f}x)")
    
    # 模拟连续放大
    for i in range(3):
        new_xlim, new_ylim = optimized_zoom(ax1, zoom_in=True, mouse_x=(new_xlim[0]+new_xlim[1])/2, mouse_y=3)
        print(f"   连续放大{i+2}: {new_xlim[0]:.0f} - {new_xlim[1]:.0f} (显示 {new_xlim[1]-new_xlim[0]:.0f} 个数据点)")
    
    # 模拟缩小操作
    for i in range(2):
        new_xlim, new_ylim = optimized_zoom(ax1, zoom_in=False, mouse_x=(new_xlim[0]+new_xlim[1])/2, mouse_y=3)
        print(f"   缩小{i+1}: {new_xlim[0]:.0f} - {new_xlim[1]:.0f}")
    
    ax1.set_ylabel('Tension (N)')
    ax1.set_title('优化后的缩放功能测试')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    ax2.set_xlabel('Time (seconds)')
    ax2.set_ylabel('Vibration (Hz)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('优化缩放测试.png', dpi=150, bbox_inches='tight')
    print(f"\n测试图表已保存为 '优化缩放测试.png'")
    plt.close()

def compare_zoom_methods():
    """对比优化前后的缩放方法"""
    print("\n" + "="*60)
    print("缩放功能优化对比:")
    print("="*60)
    
    print("🔧 优化前的问题:")
    print("   ❌ 缩放步长固定 (1.1倍)，操作不够平滑")
    print("   ❌ X轴和Y轴使用相同缩放因子，比例不协调")
    print("   ❌ 没有边界检查，可能缩放过度")
    print("   ❌ 鼠标位置处理不够健壮")
    print("   ❌ 只有滚轮操作，缺少其他控制方式")
    
    print("\n✅ 优化后的改进:")
    print("   ✅ 不同方向使用不同缩放因子 (X轴0.8/1.25, Y轴0.9/1.1)")
    print("   ✅ 更平滑的缩放体验，操作更自然")
    print("   ✅ 智能边界检查，防止过度缩放")
    print("   ✅ 鼠标位置异常处理，回退到中心点")
    print("   ✅ 添加键盘快捷键支持")
    print("   ✅ 使用draw_idle()提升性能")

def test_keyboard_shortcuts():
    """测试键盘快捷键功能"""
    print("\n" + "="*60)
    print("键盘快捷键功能:")
    print("="*60)
    
    shortcuts = [
        {"key": "+/=", "function": "放大图表", "description": "以当前视图中心为基准放大"},
        {"key": "-", "function": "缩小图表", "description": "以当前视图中心为基准缩小"},
        {"key": "←", "function": "向左平移", "description": "向左平移当前视图范围的10%"},
        {"key": "→", "function": "向右平移", "description": "向右平移当前视图范围的10%"},
        {"key": "R", "function": "重置缩放", "description": "恢复到完整数据视图"},
    ]
    
    for shortcut in shortcuts:
        key = shortcut["key"]
        func = shortcut["function"]
        desc = shortcut["description"]
        print(f"   🎹 {key:3s} : {func:8s} - {desc}")

def test_zoom_performance():
    """测试缩放性能优化"""
    print("\n" + "="*60)
    print("性能优化:")
    print("="*60)
    
    optimizations = [
        "使用 draw_idle() 替代 draw() 提升响应速度",
        "智能边界检查减少不必要的计算",
        "缓存鼠标位置避免重复计算",
        "分离X轴和Y轴缩放逻辑提高效率",
        "键盘事件处理优化减少延迟"
    ]
    
    for i, opt in enumerate(optimizations, 1):
        print(f"   ⚡ {i}. {opt}")

def main():
    print("异常检测系统 - 缩放功能优化测试")
    print("="*60)
    
    # 运行所有测试
    test_zoom_improvements()
    compare_zoom_methods()
    test_keyboard_shortcuts()
    test_zoom_performance()
    
    print("\n" + "="*60)
    print("优化总结:")
    print("="*60)
    print("🎯 主要改进:")
    print("   1. 更平滑的缩放体验 - 不同轴向使用优化的缩放因子")
    print("   2. 智能边界控制 - 防止过度缩放和超出数据范围")
    print("   3. 多种操作方式 - 鼠标滚轮 + 键盘快捷键")
    print("   4. 增强的平移功能 - 左右箭头键快速平移")
    print("   5. 性能优化 - 使用draw_idle()提升响应速度")
    
    print("\n🎮 操作指南:")
    print("   • 鼠标滚轮: 以鼠标位置为中心缩放")
    print("   • +/- 键: 以视图中心缩放")
    print("   • ←→ 键: 左右平移视图")
    print("   • R 键: 重置到完整视图")
    print("   • 点击: 查看数据点详情")
    print("="*60)

if __name__ == '__main__':
    main()
