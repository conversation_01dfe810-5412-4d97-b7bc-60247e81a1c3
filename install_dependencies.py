#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ 成功安装 {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ 安装 {package} 失败")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    print("检查和安装依赖包...")
    print("=" * 50)
    
    # 需要安装的包
    packages = {
        'PySide6': 'PySide6',
        'pandas': 'pandas',
        'numpy': 'numpy',
        'matplotlib': 'matplotlib',
        'scipy': 'scipy'
    }
    
    # 可选包（用于高级版本）
    optional_packages = {
        'pyqtgraph': 'pyqtgraph'
    }
    
    all_success = True
    
    # 检查和安装必需包
    print("安装必需包:")
    for import_name, package_name in packages.items():
        if check_package(import_name):
            print(f"✓ {package_name} 已安装")
        else:
            print(f"正在安装 {package_name}...")
            if not install_package(package_name):
                all_success = False
    
    print("\n安装可选包:")
    # 检查和安装可选包
    for import_name, package_name in optional_packages.items():
        if check_package(import_name):
            print(f"✓ {package_name} 已安装")
        else:
            print(f"正在安装 {package_name}...")
            install_package(package_name)  # 可选包安装失败不影响主程序
    
    print("\n" + "=" * 50)
    if all_success:
        print("✓ 所有必需依赖已安装完成！")
        print("\n运行程序:")
        print("  基础版本: python simple_plotter.py")
        print("  高级版本: python vibration_plotter.py")
    else:
        print("✗ 部分依赖安装失败，请手动安装")
        print("  可以尝试运行: pip install -r requirements.txt")

if __name__ == '__main__':
    main()
