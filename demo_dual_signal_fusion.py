#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双信号融合分析演示脚本
展示如何在anomaly_detector.py中使用双信号融合分析功能
"""

import sys
import pandas as pd
import numpy as np
from PySide6.QtWidgets import QApplication

# 确保能导入anomaly_detector
try:
    from anomaly_detector import AnomalyDetector
    print("✅ 成功导入 AnomalyDetector")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def create_demo_data():
    """创建演示数据"""
    print("📊 创建演示数据...")
    
    # 生成1000个数据点
    n_points = 1000
    time = np.arange(n_points)
    
    # 基础信号
    np.random.seed(123)
    tension = 100 + 5 * np.sin(time * 0.02) + np.random.normal(0, 1.5, n_points)
    vib_x = 0.3 * np.sin(time * 0.03) + np.random.normal(0, 0.05, n_points)
    vib_y = 0.2 * np.cos(time * 0.025) + np.random.normal(0, 0.04, n_points)
    vib_z = 0.25 * np.sin(time * 0.035) + np.random.normal(0, 0.06, n_points)
    
    # 添加几个明显的异常点
    anomaly_points = [150, 350, 550, 750]
    for point in anomaly_points:
        # 张力异常
        tension[point:point+3] += 30
        # 振动异常（稍有延迟）
        vib_x[point+1:point+4] += 1.0
        vib_y[point+1:point+4] += 0.8
        vib_z[point+1:point+4] += 1.2
    
    # 创建DataFrame
    data = pd.DataFrame({
        'Tension': tension,
        'WT1_X': vib_x,
        'WT1_Y': vib_y,
        'WT1_Z': vib_z,
        'WT2_X': vib_x * 0.8 + np.random.normal(0, 0.02, n_points),
        'WT2_Y': vib_y * 0.9 + np.random.normal(0, 0.02, n_points),
        'WT2_Z': vib_z * 0.85 + np.random.normal(0, 0.02, n_points),
        'Speed': np.ones(n_points) * 12
    })
    
    print(f"   ✓ 生成 {n_points} 个数据点")
    print(f"   ✓ 添加异常点: {anomaly_points}")
    print(f"   ✓ 包含传感器: WT1, WT2 (X/Y/Z轴)")
    
    return data, anomaly_points

def demo_dual_signal_fusion():
    """演示双信号融合分析功能"""
    print("=== 双信号融合分析演示 ===\n")
    
    # 创建QApplication (GUI需要)
    app = QApplication(sys.argv)
    
    try:
        # 创建演示数据
        demo_data, true_anomalies = create_demo_data()
        
        # 创建异常检测器
        print("\n🔧 初始化异常检测器...")
        detector = AnomalyDetector()
        
        # 加载演示数据
        detector.data = demo_data
        detector.time_data = np.arange(len(demo_data))
        
        # 配置检测参数
        print("\n⚙️ 配置检测参数...")
        
        # 选择WT1传感器
        detector.sensor_checkboxes['WT1'].setChecked(True)
        for sensor in ['WT2', 'WT3', 'WT4', 'WT5', 'WT6']:
            detector.sensor_checkboxes[sensor].setChecked(False)
        
        # 选择所有轴向
        for axis in ['X', 'Y', 'Z']:
            detector.axis_checkboxes[axis].setChecked(True)
        
        # 设置为双信号融合分析
        detector.method_combo.setCurrentText("双信号融合分析")
        detector.sensitivity_spin.setValue(3)  # 中等敏感度
        
        print("   ✓ 传感器: WT1")
        print("   ✓ 轴向: X, Y, Z")
        print("   ✓ 方法: 双信号融合分析")
        print("   ✓ 敏感度: 3 (中等)")
        
        # 执行异常检测
        print("\n🔍 执行双信号融合分析...")
        detector.perform_anomaly_detection()
        
        # 分析结果
        print("\n📈 分析结果:")
        print("=" * 50)
        
        total_detected = 0
        for key, anomalies in detector.anomalies.items():
            count = np.sum(anomalies)
            total_detected += count
            
            if count > 0:
                positions = np.where(anomalies)[0]
                print(f"\n{key}:")
                print(f"  检测数量: {count}")
                print(f"  异常位置: {positions[:10].tolist()}")  # 显示前10个
                
                # 计算准确性
                matches = 0
                for true_pos in true_anomalies:
                    for det_pos in positions:
                        if abs(det_pos - true_pos) <= 5:  # 允许±5的误差
                            matches += 1
                            break
                
                if len(positions) > 0:
                    precision = matches / len(positions)
                    recall = matches / len(true_anomalies)
                    print(f"  精确率: {precision:.2f}")
                    print(f"  召回率: {recall:.2f}")
        
        print(f"\n总检测异常数: {total_detected}")
        print(f"真实异常数: {len(true_anomalies)}")
        
        # 显示算法特点
        print("\n🎯 双信号融合分析特点:")
        print("• 数据预处理: 自动去除突发性噪声")
        print("• MAAD计算: 滑动窗口平均绝对偏差分析")
        print("• 三轴融合: 综合X/Y/Z轴振动信息")
        print("• 张力检测: L1突发+L2趋势双重检测")
        print("• 时间关联: 1秒延迟窗口内信号融合")
        print("• 误报控制: 双信号验证机制")
        
        # 更新显示
        print("\n🖥️ 更新图形显示...")
        detector.update_analysis()
        
        print("\n✅ 双信号融合分析演示完成!")
        print("\n💡 提示:")
        print("- 可以通过GUI界面进一步调整参数")
        print("- 支持鼠标滚轮缩放和点击查看详情")
        print("- 可以导出异常检测结果到CSV文件")
        
        # 显示GUI (可选)
        show_gui = input("\n是否显示GUI界面? (y/n): ").lower().strip()
        if show_gui == 'y':
            print("🖼️ 启动GUI界面...")
            detector.show()
            app.exec()
        else:
            print("👋 演示结束")
            
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        app.quit()

if __name__ == '__main__':
    demo_dual_signal_fusion()
