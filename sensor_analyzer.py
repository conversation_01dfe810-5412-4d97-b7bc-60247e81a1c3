import sys
import pandas as pd
import numpy as np
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import Qt
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

class SensorAnalyzer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("传感器振动数据分析")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 创建matplotlib图形
        self.figure = Figure(figsize=(12, 8))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        # 加载和分析数据
        self.load_and_analyze_data()
    
    def load_and_analyze_data(self):
        # 读取CSV数据
        df = pd.read_csv('2025-07-24-WT1.csv')
        
        # 创建子图
        axes = self.figure.subplots(2, 2)
        axes = axes.flatten()
        
        sensors = ['X_Acceler', 'Y_Acceler', 'Z_Acceler']
        colors = ['blue', 'green', 'red']
        
        # 分析每个传感器数据
        for i, sensor in enumerate(sensors):
            data = df[sensor].values
            time_indices = range(len(data))
            
            # 绘制原始数据
            axes[i].plot(time_indices, data, color=colors[i], 
                        linewidth=1, label=f'{sensor}')
            
            # 检测异常点 - 使用3σ准则
            mean_val = np.mean(data)
            std_val = np.std(data)
            threshold = 2.5 * std_val  # 可调整阈值
            
            anomalies = np.where(np.abs(data - mean_val) > threshold)[0]
            
            if len(anomalies) > 0:
                axes[i].scatter(anomalies, data[anomalies], 
                              color='red', s=30, zorder=5, 
                              label=f'异常点 ({len(anomalies)}个)')
            
            axes[i].set_title(f'{sensor} 振动数据')
            axes[i].set_xlabel('时间点')
            axes[i].set_ylabel('加速度值')
            axes[i].grid(True, alpha=0.3)
            axes[i].legend()
        
        # 综合分析图
        magnitude = np.sqrt(df['X_Acceler']**2 + df['Y_Acceler']**2 + df['Z_Acceler']**2)
        axes[3].plot(range(len(magnitude)), magnitude, 'purple', linewidth=1)
        
        # 综合异常检测
        mag_mean = np.mean(magnitude)
        mag_std = np.std(magnitude)
        mag_anomalies = np.where(np.abs(magnitude - mag_mean) > 2.5 * mag_std)[0]
        
        if len(mag_anomalies) > 0:
            axes[3].scatter(mag_anomalies, magnitude[mag_anomalies], 
                          color='red', s=30, zorder=5)
        
        axes[3].set_title('综合振动幅值')
        axes[3].set_xlabel('时间点')
        axes[3].set_ylabel('振动幅值')
        axes[3].grid(True, alpha=0.3)
        
        self.figure.tight_layout()
        self.canvas.draw()

        # 保存识别后的图片
        self.save_analysis_image()

    def save_analysis_image(self):
        """保存分析结果图片"""
        import os
        from datetime import datetime

        # 创建保存目录
        save_dir = "analysis_results"
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        # 生成文件名（包含时间戳）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"sensor_analysis_{timestamp}.png"
        filepath = os.path.join(save_dir, filename)

        # 保存图片
        self.figure.savefig(filepath, dpi=300, bbox_inches='tight',
                           facecolor='white', edgecolor='none')

        print(f"分析结果已保存到: {filepath}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = SensorAnalyzer()
    window.show()
    sys.exit(app.exec())