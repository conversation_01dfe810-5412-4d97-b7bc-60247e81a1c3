# 异常检测功能指南

## 🎯 功能概述

异常检测器能够自动分析张力和振动数据，识别并标记出异常点，帮助您快速发现设备运行中的问题。

## 🚀 启动方式

### 方法1：使用启动脚本
```bash
python run.py
# 选择选项1：Anomaly Detector
```

### 方法2：直接启动
```bash
python anomaly_detector.py
```

## 🔍 检测方法

### 1. Z-Score (标准差方法)
- **原理**：基于数据的标准差来识别异常点
- **适用**：数据呈正态分布的情况
- **阈值**：通常使用3倍标准差作为异常边界
- **优点**：计算简单，适用于大多数情况

### 2. IQR (四分位数方法)
- **原理**：使用四分位数范围来识别异常点
- **适用**：数据分布不规则的情况
- **阈值**：Q1 - 1.5×IQR 和 Q3 + 1.5×IQR
- **优点**：对异常值不敏感，更稳健

### 3. 张力-振动关联检测
- **原理**：分析张力变化与振动响应的关联性
- **适用**：检测设备运行状态异常
- **方法**：检测张力突变和振动RMS异常
- **优点**：能发现系统性问题

### 4. 组合检测
- **原理**：结合多种方法的结果
- **适用**：全面的异常检测
- **方法**：Z-Score + IQR + 关联检测
- **优点**：检测精度最高，误报率低

## ⚙️ 敏感度设置

| 敏感度 | Z-Score阈值 | 检测特点 |
|--------|-------------|----------|
| 1 (最低) | 4.0 | 只检测极端异常 |
| 2 (低) | 3.5 | 检测明显异常 |
| 3 (中等) | 3.0 | 平衡检测 |
| 4 (高) | 2.5 | 检测轻微异常 |
| 5 (最高) | 2.0 | 检测所有可疑点 |

## 📊 界面说明

### 左侧：图表显示
- **上方图表**：张力曲线 + 异常点标记（红色X）
- **下方图表**：振动曲线 + 异常点标记（红色圆圈）
- **图例**：显示异常点数量统计

### 右侧：检测结果
- **异常统计**：各传感器异常点数量
- **异常详情**：前10个异常点的时间和数值
- **分析建议**：基于异常率的建议

### 控制面板
- **传感器选择**：选择要分析的传感器
- **轴向选择**：选择要分析的轴向
- **检测方法**：选择异常检测算法
- **敏感度**：调整检测敏感度
- **导出功能**：导出异常点数据

## 🎨 视觉标记

### 异常点标记
- **张力异常**：红色X标记，大小80像素
- **振动异常**：红色圆圈标记，大小40像素
- **边框强调**：红色边框，线宽2像素

### 颜色方案
- **正常数据**：蓝色张力线，彩色振动线
- **异常标记**：统一红色标记
- **背景网格**：浅灰色，透明度30%

## 📈 分析建议

### 异常率评估
- **0%**：✅ 数据正常，无需关注
- **< 5%**：⚠️ 少量异常，建议监控
- **≥ 5%**：🚨 大量异常，需要详细检查

### 异常类型分析
1. **张力异常**
   - 突然增大：可能是负载增加或阻力增大
   - 突然减小：可能是松弛或断裂风险
   - 频繁波动：可能是控制系统问题

2. **振动异常**
   - 幅度异常：可能是不平衡或松动
   - 频率异常：可能是共振或磨损
   - 多轴异常：可能是结构问题

## 💾 数据导出

### 导出内容
- **时间点**：异常发生的时间
- **数据类型**：张力或具体传感器轴向
- **数值**：异常点的具体数值
- **检测方法**：使用的检测算法

### 导出格式
```csv
Time_Point,Data_Type,Value,Detection_Method
45,Tension,25.6789,Z-Score (Standard Deviation)
67,WT1_X,-0.1234,Z-Score (Standard Deviation)
89,WT2_Y,1.5678,Z-Score (Standard Deviation)
```

## 🔧 使用技巧

### 1. 选择合适的检测方法
- **数据稳定**：使用Z-Score方法
- **数据波动大**：使用IQR方法
- **系统分析**：使用关联检测
- **全面检测**：使用组合检测

### 2. 调整敏感度
- **初次分析**：使用中等敏感度(3)
- **发现问题少**：提高敏感度(4-5)
- **误报太多**：降低敏感度(1-2)

### 3. 传感器选择
- **重点监控**：只选择关键传感器
- **全面分析**：选择所有传感器
- **对比分析**：选择相关传感器组合

### 4. 结果解读
- **关注异常集中区域**：可能是系统性问题
- **关注异常模式**：周期性异常可能是设计问题
- **关注异常幅度**：大幅异常需要优先处理

## 🚨 注意事项

1. **数据质量**：确保输入数据的准确性
2. **参数设置**：根据实际情况调整检测参数
3. **结果验证**：异常检测结果需要人工验证
4. **定期校准**：定期更新检测阈值和参数
5. **综合分析**：结合其他分析方法进行综合判断

## 📚 相关文档

- `README.md` - 项目总体说明
- `USAGE_GUIDE.md` - 基础使用指南
- `INTERACTIVE_FEATURES.md` - 交互功能说明
- `FINAL_SUMMARY.md` - 项目完整总结

---

**💡 提示：异常检测是数据分析的重要工具，但需要结合专业知识进行判断！**
