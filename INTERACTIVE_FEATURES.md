# 交互功能说明

## 🎯 新增功能特性

### 1. 组合显示布局
- **上下分布**：张力曲线显示在上方，振动曲线显示在下方
- **共享时间轴**：两个图表使用相同的时间轴，便于对比分析
- **统一缩放**：缩放时间轴时，两个图表同步变化

### 2. 鼠标交互辅助线
- **十字辅助线**：鼠标移动时显示灰色虚线十字线
- **垂直线同步**：垂直辅助线在两个图表中同步显示
- **水平线独立**：每个图表有独立的水平辅助线
- **实时跟踪**：辅助线实时跟随鼠标位置

### 3. 数据点击获取
- **精确数值**：点击图表任意位置获取该时间点的精确数据
- **控制台输出**：数据值在控制台中格式化显示
- **多传感器支持**：同时显示所有选中传感器的数据
- **时间定位**：显示点击位置对应的时间点

## 🖱️ 使用方法

### 鼠标操作
1. **移动鼠标**：在图表区域移动鼠标查看辅助线
2. **点击获取数据**：点击图表任意位置获取数据值
3. **缩放**：使用鼠标滚轮缩放图表
4. **平移**：按住鼠标左键拖动平移图表

### 控制面板操作
1. **选择传感器**：勾选要显示的传感器（WT1-WT6）
2. **选择轴向**：勾选要显示的轴向（X、Y、Z）
3. **更新图表**：点击"Update Charts"刷新显示

## 📊 数据输出格式

当您点击图表时，控制台会输出如下格式的数据：

```
📍 Time point: 181 seconds
🔧 Tension: 9.80 N
📊 Vibration Data:
   WT1_X: -0.0273 g
   WT1_Y: -0.0952 g
   WT1_Z: 0.9966 g
   WT2_X: -1.0112 g
   WT2_Y: -0.0767 g
   WT2_Z: 0.0527 g
   WT3_X: -0.0122 g
   WT3_Y: 0.0381 g
   WT3_Z: 0.9985 g
========================================
```

## 🎨 视觉特性

### 颜色方案
- **张力曲线**：红色实线，线宽2.5
- **振动曲线**：6种不同颜色区分传感器
  - WT1: 红色 (#FF6B6B)
  - WT2: 青色 (#4ECDC4)
  - WT3: 蓝色 (#45B7D1)
  - WT4: 橙色 (#FFA07A)
  - WT5: 绿色 (#98D8C8)
  - WT6: 黄色 (#F7DC6F)

### 线型样式
- **X轴数据**：实线 (-)
- **Y轴数据**：虚线 (--)
- **Z轴数据**：点线 (:)

### 辅助线样式
- **颜色**：灰色
- **样式**：虚线
- **透明度**：70%
- **线宽**：1像素

## 🔧 技术实现

### 组合显示
```python
# 创建上下两个子图，共享x轴
ax1 = fig.add_subplot(211)  # 张力图
ax2 = fig.add_subplot(212, sharex=ax1)  # 振动图
```

### 鼠标事件处理
```python
# 连接鼠标事件
canvas.mpl_connect('motion_notify_event', on_mouse_move)
canvas.mpl_connect('button_press_event', on_mouse_click)
```

### 辅助线更新
```python
# 更新辅助线位置
crosshair_v.set_xdata([event.xdata, event.xdata])
crosshair_h.set_ydata([event.ydata, event.ydata])
```

## 💡 使用技巧

### 数据分析
1. **时间关联分析**：通过垂直辅助线对比同一时间点的张力和振动数据
2. **趋势观察**：观察张力变化与振动幅度的关系
3. **异常检测**：通过点击获取异常点的精确数值
4. **数据导出**：控制台输出的数据可以复制用于进一步分析

### 性能优化
1. **选择性显示**：只选择需要的传感器和轴向以提高性能
2. **数据范围**：对于大数据集，可以先缩放到感兴趣的时间范围
3. **图表刷新**：修改选择后点击"Update Charts"手动刷新

## 🚀 版本对比

| 功能 | 组合分析器 | 基础版本 | 高级版本 |
|------|------------|----------|----------|
| 组合显示 | ✅ | ❌ | ✅ |
| 交互辅助线 | ✅ | ❌ | ✅ |
| 数据点击 | ✅ | ❌ | ✅ |
| 性能 | 中等 | 中等 | 高 |
| 兼容性 | 好 | 最好 | 好 |

**推荐使用组合分析器**进行日常数据分析工作！
