#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import pandas as pd
import numpy as np
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                               QHBoxLayout, QWidget, QPushButton, QLabel,
                               QCheckBox, QGroupBox, QGridLayout, QTabWidget)
from PySide6.QtCore import Qt
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from datetime import datetime, timedelta

class MatplotlibCanvas(FigureCanvas):
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        super().__init__(self.fig)
        self.setParent(parent)

class VibrationAnalyzer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.data = None
        self.time_data = None
        self.init_ui()
        self.load_default_data()
        
    def init_ui(self):
        self.setWindowTitle('Vibration Data Analyzer')
        self.setGeometry(100, 100, 1200, 800)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Control panel
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)
        
        # Create tabs
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Tension curve tab
        self.tension_tab = QWidget()
        self.tab_widget.addTab(self.tension_tab, "Tension Curve")
        tension_layout = QVBoxLayout(self.tension_tab)
        
        self.tension_canvas = MatplotlibCanvas(self, width=10, height=6)
        tension_layout.addWidget(self.tension_canvas)
        
        # Vibration curve tab
        self.vibration_tab = QWidget()
        self.tab_widget.addTab(self.vibration_tab, "Vibration Curves")
        vibration_layout = QVBoxLayout(self.vibration_tab)
        
        self.vibration_canvas = MatplotlibCanvas(self, width=10, height=6)
        vibration_layout.addWidget(self.vibration_canvas)
        
    def create_control_panel(self):
        control_group = QGroupBox("Control Panel")
        layout = QGridLayout(control_group)
        
        # Sensor selection
        sensor_label = QLabel("Select Sensors:")
        layout.addWidget(sensor_label, 0, 0)
        
        self.sensor_checkboxes = {}
        sensors = ['WT1', 'WT2', 'WT3', 'WT4', 'WT5', 'WT6']
        
        for i, sensor in enumerate(sensors):
            checkbox = QCheckBox(sensor)
            checkbox.setChecked(i < 3)  # Default: select first 3 sensors
            checkbox.stateChanged.connect(self.update_plots)
            self.sensor_checkboxes[sensor] = checkbox
            layout.addWidget(checkbox, 0, i + 1)
        
        # Axis selection
        axis_label = QLabel("Select Axes:")
        layout.addWidget(axis_label, 1, 0)
        
        self.axis_checkboxes = {}
        axes = ['X', 'Y', 'Z']
        for i, axis in enumerate(axes):
            checkbox = QCheckBox(axis)
            checkbox.setChecked(True)
            checkbox.stateChanged.connect(self.update_plots)
            self.axis_checkboxes[axis] = checkbox
            layout.addWidget(checkbox, 1, i + 1)
        
        # Update button
        update_button = QPushButton("Update Charts")
        update_button.clicked.connect(self.update_plots)
        layout.addWidget(update_button, 2, 0)
        
        return control_group
    
    def load_default_data(self):
        """Load default CSV file"""
        try:
            self.data = pd.read_csv("7-24-20.csv")
            
            # Process time data - create relative time (seconds)
            self.time_data = np.arange(len(self.data))
            
            self.update_plots()
        except Exception as e:
            print(f"Cannot load default file: {str(e)}")
    
    def update_plots(self):
        """Update charts"""
        if self.data is None:
            return
        
        self.update_tension_plot()
        self.update_vibration_plot()
    
    def update_tension_plot(self):
        """Update tension chart"""
        self.tension_canvas.fig.clear()
        ax = self.tension_canvas.fig.add_subplot(111)
        
        if 'Tension' in self.data.columns:
            tension_data = self.data['Tension'].values
            ax.plot(self.time_data, tension_data, 'r-', linewidth=2, label='Tension')
            ax.set_xlabel('Time (seconds)')
            ax.set_ylabel('Tension (N)')
            ax.set_title('Tension vs Time')
            ax.grid(True, alpha=0.3)
            ax.legend()
        
        self.tension_canvas.draw()
    
    def update_vibration_plot(self):
        """Update vibration chart"""
        self.vibration_canvas.fig.clear()
        ax = self.vibration_canvas.fig.add_subplot(111)
        
        # Get selected sensors and axes
        selected_sensors = [sensor for sensor, checkbox in self.sensor_checkboxes.items() 
                          if checkbox.isChecked()]
        selected_axes = [axis for axis, checkbox in self.axis_checkboxes.items() 
                        if checkbox.isChecked()]
        
        colors = ['red', 'green', 'blue', 'orange', 'purple', 'brown']
        line_styles = ['-', '--', ':']
        
        # Plot selected sensor data
        plot_count = 0
        for i, sensor in enumerate(selected_sensors):
            for j, axis in enumerate(selected_axes):
                column_name = f"{sensor}_{axis}"
                if column_name in self.data.columns:
                    vibration_data = self.data[column_name].values
                    color = colors[i % len(colors)]
                    style = line_styles[j % len(line_styles)]
                    
                    ax.plot(self.time_data, vibration_data,
                           color=color, linestyle=style, linewidth=1.5,
                           label=f"{sensor}_{axis}", alpha=0.8)
                    plot_count += 1
        
        if plot_count > 0:
            ax.set_xlabel('Time (seconds)')
            ax.set_ylabel('Vibration Amplitude (g)')
            ax.set_title('Vibration Data vs Time')
            ax.grid(True, alpha=0.3)
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        self.vibration_canvas.fig.tight_layout()
        self.vibration_canvas.draw()

def main():
    app = QApplication(sys.argv)
    
    window = VibrationAnalyzer()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
