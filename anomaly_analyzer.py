#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# Anomaly Detection for Tension and Vibration Data

import sys
import pandas as pd
import numpy as np
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                               QHBoxLayout, QWidget, QPushButton, QLabel,
                               QCheckBox, QGroupBox, QGridLayout, QTextEdit,
                               QSplitter, QSpinBox, QComboBox)
from PySide6.QtCore import Qt
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class MatplotlibCanvas(FigureCanvas):
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        super().__init__(self.fig)
        self.setParent(parent)

class AnomalyAnalyzer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.data = None
        self.time_data = None
        self.anomalies = {}
        self.crosshair_v = None
        self.crosshair_h1 = None
        self.crosshair_h2 = None
        self.mouse_connected = False
        self.init_ui()
        self.load_default_data()
        
    def init_ui(self):
        self.setWindowTitle('振动数据异常检测分析器')
        self.setGeometry(100, 100, 1600, 1000)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        central_widget_layout = QVBoxLayout(central_widget)
        central_widget_layout.addWidget(splitter)
        
        # 左侧：控制面板和图表
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 控制面板
        control_panel = self.create_control_panel()
        left_layout.addWidget(control_panel)
        
        # 图表
        self.combined_canvas = MatplotlibCanvas(self, width=12, height=10)
        left_layout.addWidget(self.combined_canvas)
        
        # 右侧：异常检测结果
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 异常检测结果标题
        result_label = QLabel("异常检测结果")
        result_label.setStyleSheet("font-size: 14px; font-weight: bold; color: red;")
        right_layout.addWidget(result_label)
        
        # 异常检测结果文本框
        self.result_text = QTextEdit()
        self.result_text.setMaximumWidth(400)
        self.result_text.setStyleSheet("font-family: monospace; font-size: 10px;")
        right_layout.addWidget(self.result_text)
        
        # 添加到分割器
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([1200, 400])
        
    def create_control_panel(self):
        control_group = QGroupBox("控制面板")
        layout = QGridLayout(control_group)
        
        # 传感器选择
        sensor_label = QLabel("选择传感器:")
        layout.addWidget(sensor_label, 0, 0)
        
        self.sensor_checkboxes = {}
        sensors = ['WT1', 'WT2', 'WT3', 'WT4', 'WT5', 'WT6']
        
        for i, sensor in enumerate(sensors):
            checkbox = QCheckBox(sensor)
            checkbox.setChecked(i < 3)
            checkbox.stateChanged.connect(self.update_analysis)
            self.sensor_checkboxes[sensor] = checkbox
            layout.addWidget(checkbox, 0, i + 1)
        
        # 轴选择
        axis_label = QLabel("选择轴:")
        layout.addWidget(axis_label, 1, 0)
        
        self.axis_checkboxes = {}
        axes = ['X', 'Y', 'Z']
        for i, axis in enumerate(axes):
            checkbox = QCheckBox(axis)
            checkbox.setChecked(True)
            checkbox.stateChanged.connect(self.update_analysis)
            self.axis_checkboxes[axis] = checkbox
            layout.addWidget(checkbox, 1, i + 1)
        
        # 异常检测方法选择
        method_label = QLabel("检测方法:")
        layout.addWidget(method_label, 2, 0)
        
        self.method_combo = QComboBox()
        self.method_combo.addItems([
            "Z-Score (标准差)", 
            "IQR (四分位数)", 
            "张力-振动关联",
            "组合检测"
        ])
        self.method_combo.currentTextChanged.connect(self.update_analysis)
        layout.addWidget(self.method_combo, 2, 1, 1, 2)
        
        # 敏感度设置
        sensitivity_label = QLabel("敏感度:")
        layout.addWidget(sensitivity_label, 2, 3)
        
        self.sensitivity_spin = QSpinBox()
        self.sensitivity_spin.setRange(1, 5)
        self.sensitivity_spin.setValue(3)
        self.sensitivity_spin.valueChanged.connect(self.update_analysis)
        layout.addWidget(self.sensitivity_spin, 2, 4)
        
        # 更新按钮
        update_button = QPushButton("更新分析")
        update_button.clicked.connect(self.update_analysis)
        layout.addWidget(update_button, 3, 0)
        
        # 导出异常点按钮
        export_button = QPushButton("导出异常点")
        export_button.clicked.connect(self.export_anomalies)
        layout.addWidget(export_button, 3, 1)
        
        return control_group

    def load_default_data(self):
        """加载默认的CSV文件"""
        try:
            self.data = pd.read_csv("7-24-20.csv")
            self.time_data = np.arange(len(self.data))
            self.update_analysis()
        except Exception as e:
            print(f"无法加载默认文件: {str(e)}")

    def detect_anomalies_zscore(self, data, threshold=3):
        """使用Z-Score方法检测异常"""
        z_scores = np.abs(stats.zscore(data))
        return z_scores > threshold

    def detect_anomalies_iqr(self, data):
        """使用IQR方法检测异常"""
        Q1 = np.percentile(data, 25)
        Q3 = np.percentile(data, 75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        return (data < lower_bound) | (data > upper_bound)

    def detect_tension_vibration_correlation(self):
        """检测张力和振动的异常关联"""
        if 'Tension' not in self.data.columns:
            return np.array([])

        tension = self.data['Tension'].values
        anomalies = np.zeros(len(tension), dtype=bool)

        # 获取选中的振动数据
        selected_sensors = [sensor for sensor, checkbox in self.sensor_checkboxes.items()
                          if checkbox.isChecked()]
        selected_axes = [axis for axis, checkbox in self.axis_checkboxes.items()
                        if checkbox.isChecked()]

        vibration_data = []
        for sensor in selected_sensors:
            for axis in selected_axes:
                column_name = f"{sensor}_{axis}"
                if column_name in self.data.columns:
                    vibration_data.append(self.data[column_name].values)

        if not vibration_data:
            return anomalies

        # 计算振动数据的综合指标（RMS）
        vibration_rms = np.sqrt(np.mean([v**2 for v in vibration_data], axis=0))

        # 检测张力突变点
        tension_diff = np.abs(np.diff(tension))
        tension_anomalies = tension_diff > np.percentile(tension_diff, 95)

        # 检测振动异常点
        vibration_anomalies = self.detect_anomalies_iqr(vibration_rms)

        # 组合异常检测
        anomalies[1:] = tension_anomalies
        anomalies = anomalies | vibration_anomalies

        return anomalies

    def perform_anomaly_detection(self):
        """执行异常检测"""
        if self.data is None:
            return

        method = self.method_combo.currentText()
        sensitivity = self.sensitivity_spin.value()

        # 根据敏感度调整阈值
        threshold_map = {1: 4, 2: 3.5, 3: 3, 4: 2.5, 5: 2}
        threshold = threshold_map[sensitivity]

        self.anomalies = {}

        # 张力异常检测
        if 'Tension' in self.data.columns:
            tension_data = self.data['Tension'].values

            if "Z-Score" in method:
                tension_anomalies = self.detect_anomalies_zscore(tension_data, threshold)
            elif "IQR" in method:
                tension_anomalies = self.detect_anomalies_iqr(tension_data)
            elif "关联" in method:
                tension_anomalies = self.detect_tension_vibration_correlation()
            else:  # 组合检测
                z_anomalies = self.detect_anomalies_zscore(tension_data, threshold)
                iqr_anomalies = self.detect_anomalies_iqr(tension_data)
                corr_anomalies = self.detect_tension_vibration_correlation()
                tension_anomalies = z_anomalies | iqr_anomalies | corr_anomalies

            self.anomalies['Tension'] = tension_anomalies

        # 振动异常检测
        selected_sensors = [sensor for sensor, checkbox in self.sensor_checkboxes.items()
                          if checkbox.isChecked()]
        selected_axes = [axis for axis, checkbox in self.axis_checkboxes.items()
                        if checkbox.isChecked()]

        for sensor in selected_sensors:
            for axis in selected_axes:
                column_name = f"{sensor}_{axis}"
                if column_name in self.data.columns:
                    vibration_data = self.data[column_name].values

                    if "Z-Score" in method:
                        vibration_anomalies = self.detect_anomalies_zscore(vibration_data, threshold)
                    elif "IQR" in method:
                        vibration_anomalies = self.detect_anomalies_iqr(vibration_data)
                    elif "关联" in method:
                        vibration_anomalies = self.detect_tension_vibration_correlation()
                    else:  # 组合检测
                        z_anomalies = self.detect_anomalies_zscore(vibration_data, threshold)
                        iqr_anomalies = self.detect_anomalies_iqr(vibration_data)
                        vibration_anomalies = z_anomalies | iqr_anomalies

                    self.anomalies[column_name] = vibration_anomalies

    def update_analysis(self):
        """更新分析和图表"""
        if self.data is None:
            return

        # 执行异常检测
        self.perform_anomaly_detection()

        # 更新图表
        self.update_combined_plot()

        # 更新结果文本
        self.update_result_text()

    def update_result_text(self):
        """更新异常检测结果文本"""
        result_text = "=== 异常检测结果 ===\n\n"

        total_anomalies = 0
        for key, anomalies in self.anomalies.items():
            anomaly_count = np.sum(anomalies)
            total_anomalies += anomaly_count

            if anomaly_count > 0:
                result_text += f"{key}: {anomaly_count} 个异常点\n"

                # 显示前10个异常点的时间和数值
                anomaly_indices = np.where(anomalies)[0][:10]
                for idx in anomaly_indices:
                    time_point = idx
                    value = self.data.iloc[idx][key] if key in self.data.columns else 0
                    result_text += f"  时间 {time_point}s: {value:.4f}\n"

                if len(np.where(anomalies)[0]) > 10:
                    result_text += f"  ... 还有 {len(np.where(anomalies)[0]) - 10} 个异常点\n"
                result_text += "\n"

        result_text += f"总计异常点数: {total_anomalies}\n"
        result_text += f"异常率: {total_anomalies/len(self.data)*100:.2f}%\n\n"

        # 添加异常分析建议
        result_text += "=== 分析建议 ===\n"
        if total_anomalies == 0:
            result_text += "✅ 未检测到明显异常，数据正常\n"
        elif total_anomalies < len(self.data) * 0.05:
            result_text += "⚠️ 检测到少量异常点，建议关注\n"
        else:
            result_text += "🚨 检测到大量异常点，建议详细检查\n"

        self.result_text.setText(result_text)

    def setup_mouse_interaction(self):
        """设置鼠标交互功能"""
        if not self.mouse_connected:
            self.combined_canvas.mpl_connect('motion_notify_event', self.on_mouse_move)
            self.combined_canvas.mpl_connect('button_press_event', self.on_mouse_click)
            self.mouse_connected = True

    def on_mouse_move(self, event):
        """鼠标移动事件"""
        if event.inaxes is None:
            return

        if self.crosshair_v is not None:
            for line in self.crosshair_v:
                line.set_xdata([event.xdata, event.xdata])

        if event.inaxes == self.ax1 and self.crosshair_h1 is not None:
            self.crosshair_h1.set_ydata([event.ydata, event.ydata])
        elif event.inaxes == self.ax2 and self.crosshair_h2 is not None:
            self.crosshair_h2.set_ydata([event.ydata, event.ydata])

        self.combined_canvas.draw_idle()

    def on_mouse_click(self, event):
        """鼠标点击事件"""
        if event.inaxes is None:
            return

        x_pos = int(event.xdata) if event.xdata is not None else 0
        if 0 <= x_pos < len(self.data):
            print(f"\n📍 时间点: {x_pos}秒")

            # 检查是否为异常点
            is_anomaly = False
            for key, anomalies in self.anomalies.items():
                if x_pos < len(anomalies) and anomalies[x_pos]:
                    print(f"🚨 异常检测: {key} 在此时间点异常")
                    is_anomaly = True

            if not is_anomaly:
                print("✅ 此时间点数据正常")

            if 'Tension' in self.data.columns:
                tension_val = self.data.iloc[x_pos]['Tension']
                print(f"🔧 张力: {tension_val:.2f} N")

            # 显示振动数据
            selected_sensors = [sensor for sensor, checkbox in self.sensor_checkboxes.items()
                              if checkbox.isChecked()]
            selected_axes = [axis for axis, checkbox in self.axis_checkboxes.items()
                            if checkbox.isChecked()]

            print("📊 振动数据:")
            for sensor in selected_sensors:
                for axis in selected_axes:
                    column_name = f"{sensor}_{axis}"
                    if column_name in self.data.columns:
                        value = self.data.iloc[x_pos][column_name]
                        anomaly_mark = "🚨" if column_name in self.anomalies and x_pos < len(self.anomalies[column_name]) and self.anomalies[column_name][x_pos] else "✅"
                        print(f"   {anomaly_mark} {sensor}_{axis}: {value:.4f} g")
            print("=" * 50)

    def update_combined_plot(self):
        """更新组合图表并标记异常点"""
        if self.data is None:
            return

        self.combined_canvas.fig.clear()

        # 创建子图
        self.ax1 = self.combined_canvas.fig.add_subplot(211)  # 张力图
        self.ax2 = self.combined_canvas.fig.add_subplot(212, sharex=self.ax1)  # 振动图

        # 绘制张力曲线
        if 'Tension' in self.data.columns:
            tension_data = self.data['Tension'].values
            self.ax1.plot(self.time_data, tension_data, 'b-', linewidth=2, label='张力', alpha=0.8)

            # 标记张力异常点
            if 'Tension' in self.anomalies:
                anomaly_indices = np.where(self.anomalies['Tension'])[0]
                if len(anomaly_indices) > 0:
                    self.ax1.scatter(self.time_data[anomaly_indices], tension_data[anomaly_indices],
                                   color='red', s=50, marker='x', linewidth=3,
                                   label=f'张力异常点 ({len(anomaly_indices)})', zorder=5)

            self.ax1.set_ylabel('张力 (N)', fontsize=12, fontweight='bold')
            self.ax1.set_title('张力和振动数据异常检测分析', fontsize=14, fontweight='bold', pad=20)
            self.ax1.grid(True, alpha=0.3)
            self.ax1.legend(loc='upper right', fontsize=10)

        # 绘制振动曲线
        selected_sensors = [sensor for sensor, checkbox in self.sensor_checkboxes.items()
                          if checkbox.isChecked()]
        selected_axes = [axis for axis, checkbox in self.axis_checkboxes.items()
                        if checkbox.isChecked()]

        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8', '#F7DC6F']
        line_styles = ['-', '--', ':']

        plot_count = 0
        total_vibration_anomalies = 0

        for i, sensor in enumerate(selected_sensors):
            for j, axis in enumerate(selected_axes):
                column_name = f"{sensor}_{axis}"
                if column_name in self.data.columns:
                    vibration_data = self.data[column_name].values
                    color = colors[i % len(colors)]
                    style = line_styles[j % len(line_styles)]

                    # 绘制振动曲线
                    self.ax2.plot(self.time_data, vibration_data,
                                 color=color, linestyle=style, linewidth=1.5,
                                 label=f"{sensor}_{axis}", alpha=0.7)

                    # 标记振动异常点
                    if column_name in self.anomalies:
                        anomaly_indices = np.where(self.anomalies[column_name])[0]
                        if len(anomaly_indices) > 0:
                            self.ax2.scatter(self.time_data[anomaly_indices], vibration_data[anomaly_indices],
                                           color=color, s=30, marker='o', edgecolor='red', linewidth=2,
                                           zorder=5)
                            total_vibration_anomalies += len(anomaly_indices)

                    plot_count += 1

        if plot_count > 0:
            self.ax2.set_xlabel('时间 (秒)', fontsize=12, fontweight='bold')
            self.ax2.set_ylabel('振动幅度 (g)', fontsize=12, fontweight='bold')
            self.ax2.grid(True, alpha=0.3)

            # 添加异常点总数到图例
            legend_elements = self.ax2.get_legend_handles_labels()
            if total_vibration_anomalies > 0:
                self.ax2.text(0.02, 0.98, f'振动异常点总数: {total_vibration_anomalies}',
                            transform=self.ax2.transAxes, fontsize=10,
                            bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                            verticalalignment='top')

            self.ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=9)

        # 添加辅助线
        self.crosshair_v = []
        v_line1 = self.ax1.axvline(color='gray', linestyle='--', alpha=0.7, linewidth=1)
        v_line2 = self.ax2.axvline(color='gray', linestyle='--', alpha=0.7, linewidth=1)
        self.crosshair_v = [v_line1, v_line2]

        self.crosshair_h1 = self.ax1.axhline(color='gray', linestyle='--', alpha=0.7, linewidth=1)
        self.crosshair_h2 = self.ax2.axhline(color='gray', linestyle='--', alpha=0.7, linewidth=1)

        # 调整布局
        self.combined_canvas.fig.tight_layout()
        self.combined_canvas.draw()

        # 设置鼠标交互
        self.setup_mouse_interaction()

    def export_anomalies(self):
        """导出异常点数据"""
        if not self.anomalies:
            print("没有检测到异常点")
            return

        # 创建异常点数据框
        anomaly_data = []

        for key, anomalies in self.anomalies.items():
            anomaly_indices = np.where(anomalies)[0]
            for idx in anomaly_indices:
                anomaly_data.append({
                    '时间点': idx,
                    '数据类型': key,
                    '数值': self.data.iloc[idx][key] if key in self.data.columns else 0,
                    '检测方法': self.method_combo.currentText()
                })

        if anomaly_data:
            anomaly_df = pd.DataFrame(anomaly_data)
            filename = f"anomaly_detection_results.csv"
            anomaly_df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"异常点数据已导出到: {filename}")
            print(f"共导出 {len(anomaly_data)} 个异常点")
        else:
            print("没有异常点需要导出")

def main():
    app = QApplication(sys.argv)

    window = AnomalyAnalyzer()
    window.show()

    sys.exit(app.exec())

if __name__ == '__main__':
    main()
