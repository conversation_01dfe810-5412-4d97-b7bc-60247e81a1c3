# 振动数据分析器

基于PySide6的振动数据可视化工具，用于分析CSV格式的张力和振动数据。

## 功能特性

- 📈 张力曲线绘制
- 📊 多传感器振动数据可视化
- 🎛️ 交互式传感器和轴选择
- 📁 支持CSV文件加载
- 💾 图表导出功能
- 🎨 两种界面版本（基础版和高级版）

## 文件说明

- `combined_analyzer.py` - **推荐版本**（张力和振动组合显示，带交互辅助线）
- `vibration_plotter.py` - 高级版本（使用pyqtgraph，性能更好，英文界面）
- `vibration_analyzer.py` - 基础版本（使用matplotlib，兼容性更好，英文界面）
- `simple_plotter.py` - 中文版本（组合显示，可能有字体显示问题）
- `run.py` - 快速启动脚本
- `install_dependencies.py` - 依赖安装脚本
- `requirements.txt` - 依赖包列表
- `7-24-20.csv` - 示例数据文件

## 安装依赖

### 方法1：使用安装脚本（推荐）
```bash
python install_dependencies.py
```

### 方法2：手动安装
```bash
pip install -r requirements.txt
```

### 方法3：逐个安装
```bash
pip install PySide6 pandas numpy matplotlib pyqtgraph
```

## 运行程序

### 方法1：使用启动脚本（推荐）
```bash
python run.py
```

### 方法2：直接运行

#### 组合分析器（推荐，张力和振动一起显示）
```bash
python combined_analyzer.py
```

#### 基础版本（分标签页显示）
```bash
python vibration_analyzer.py
```

#### 高级版本（性能更好）
```bash
python vibration_plotter.py
```

#### 中文版本（可能有字体问题）
```bash
python simple_plotter.py
```

## 数据格式

CSV文件应包含以下列：
- `Date_Time`: 时间戳
- `Tension`: 张力值
- `WT1_X`, `WT1_Y`, `WT1_Z`: 传感器1的X、Y、Z轴数据
- `WT2_X`, `WT2_Y`, `WT2_Z`: 传感器2的X、Y、Z轴数据
- ... (最多支持6个传感器 WT1-WT6)

## 使用说明

1. **启动程序**：运行对应的Python文件
2. **选择传感器**：在控制面板中勾选要显示的传感器
3. **选择轴向**：选择要显示的X、Y、Z轴数据
4. **查看图表**：
   - "张力曲线"标签页显示张力随时间变化
   - "振动曲线"标签页显示选中传感器的振动数据
5. **更新图表**：修改选择后点击"更新图表"按钮
6. **导出图片**：点击"导出图片"按钮保存图表

## 界面说明

### 控制面板
- **传感器选择**：WT1-WT6复选框，选择要显示的传感器
- **轴选择**：X、Y、Z复选框，选择要显示的轴向数据
- **更新图表**：刷新显示
- **导出图片**：保存图表为PNG文件

### 图表显示
- **张力曲线**：红色实线显示张力随时间变化
- **振动曲线**：不同颜色和线型显示各传感器数据
  - 实线：X轴数据
  - 虚线：Y轴数据  
  - 点线：Z轴数据

## 故障排除

### 常见问题

1. **导入错误**
   ```
   ModuleNotFoundError: No module named 'PySide6'
   ```
   解决：运行 `python install_dependencies.py` 安装依赖

2. **文件加载失败**
   - 确保CSV文件格式正确
   - 检查文件路径是否正确
   - 确保文件包含必要的列

3. **图表不显示**
   - 检查是否选中了传感器和轴
   - 点击"更新图表"按钮
   - 确保数据文件包含有效数据

### 系统要求

- Python 3.7+
- 支持的操作系统：Windows、macOS、Linux
- 内存：建议2GB以上
- 显示：支持GUI界面

## 开发信息

- 开发语言：Python
- GUI框架：PySide6 (Qt6)
- 图表库：matplotlib / pyqtgraph
- 数据处理：pandas, numpy

## 许可证

本项目仅供学习和研究使用。
